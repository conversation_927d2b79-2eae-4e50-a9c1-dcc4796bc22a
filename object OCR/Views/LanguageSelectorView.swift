//
//  LanguageSelectorView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct LanguageSelectorView: View {
    @EnvironmentObject var multiLanguageService: MultiLanguageService
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标题区域
                VStack(spacing: 16) {
                    Image(systemName: "globe")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text(multiLanguageService.localizedString(for: "select_language"))
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text(multiLanguageService.localizedString(for: "language_description"))
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding(.top, 40)
                .padding(.bottom, 30)
                
                // 语言选项列表
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(multiLanguageService.getSupportedLanguages(), id: \.self) { language in
                            LanguageOptionCard(
                                language: language,
                                isSelected: language == multiLanguageService.currentLanguage,
                                onSelect: {
                                    selectLanguage(language)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                }
                
                Spacer()
                
                // 底部按钮
                VStack(spacing: 16) {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                            Text(multiLanguageService.localizedString(for: "done"))
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                    }
                    .padding(.horizontal, 20)
                    
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text(multiLanguageService.localizedString(for: "cancel"))
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.bottom, 30)
            }
            .navigationBarHidden(true)
        }
    }
    
    private func selectLanguage(_ language: MultiLanguageService.Language) {
        withAnimation(.easeInOut(duration: 0.3)) {
            multiLanguageService.switchLanguage(to: language)
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - 语言选项卡片

struct LanguageOptionCard: View {
    let language: MultiLanguageService.Language
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                // 语言旗帜
                Text(language.flag)
                    .font(.system(size: 32))
                
                // 语言信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(language.displayName)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(language.code)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 选中状态指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                } else {
                    Image(systemName: "circle")
                        .font(.title2)
                        .foregroundColor(.gray.opacity(0.3))
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - 语言切换按钮

struct LanguageSwitchButton: View {
    @EnvironmentObject var multiLanguageService: MultiLanguageService
    @State private var showingLanguageSelector = false
    
    var body: some View {
        Button(action: {
            showingLanguageSelector = true
        }) {
            HStack(spacing: 8) {
                Text(multiLanguageService.currentLanguageFlag)
                    .font(.title3)
                
                Text(multiLanguageService.currentLanguageDisplayName)
                    .font(.body)
                    .fontWeight(.medium)
                
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(20)
        }
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorView()
        }
    }
}

// MARK: - 工具栏语言按钮

struct ToolbarLanguageButton: View {
    @EnvironmentObject var multiLanguageService: MultiLanguageService
    @State private var showingLanguageSelector = false
    
    var body: some View {
        Button(action: {
            showingLanguageSelector = true
        }) {
            Image(systemName: "globe")
                .font(.title3)
                .foregroundColor(.primary)
        }
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorView()
        }
    }
}

// MARK: - 预览

struct LanguageSelectorView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            LanguageSelectorView()
                .environmentObject(MultiLanguageService())
            
            LanguageSwitchButton()
                .environmentObject(MultiLanguageService())
                .previewLayout(.sizeThatFits)
                .padding()
            
            ToolbarLanguageButton()
                .environmentObject(MultiLanguageService())
                .previewLayout(.sizeThatFits)
                .padding()
        }
    }
}
