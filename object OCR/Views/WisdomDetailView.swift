//
//  WisdomDetailView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct WisdomDetailView: View {
    @EnvironmentObject var healthWisdomService: HealthWisdomService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var selectedCategory: WisdomCategory? = nil
    @State private var searchText = ""
    @State private var showingSearch = false
    
    var filteredWisdoms: [HealthWisdom] {
        let wisdoms = healthWisdomService.uyghurHealthWisdoms
        
        if !searchText.isEmpty {
            return healthWisdomService.searchWisdom(keyword: searchText)
        }
        
        if let category = selectedCategory {
            return wisdoms.filter { $0.category == category }
        }
        
        return wisdoms
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                if showingSearch {
                    SearchBar(text: $searchText)
                        .padding()
                        .background(Color(.systemGray6))
                }
                
                // 分类选择器
                CategorySelector(selectedCategory: $selectedCategory)
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                
                // 格言列表
                ScrollView {
                    LazyVStack(spacing: 16) {
                        ForEach(filteredWisdoms) { wisdom in
                            WisdomCard(wisdom: wisdom)
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("健康格言")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: HStack {
                    Button(action: {
                        withAnimation {
                            showingSearch.toggle()
                        }
                    }) {
                        Image(systemName: "magnifyingglass")
                    }
                    
                    Button(action: {
                        // 分享功能
                        shareWisdom()
                    }) {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            )
        }
    }
    
    private func shareWisdom() {
        guard let wisdom = healthWisdomService.dailyWisdom else { return }
        
        let shareText = """
        每日健康格言 / ھەر كۈنلۈك ساغلاملىق ھېكمىتى
        
        \(wisdom.uyghur)
        
        \(wisdom.chinese)
        
        \(wisdom.english)
        
        —— 智能健康助手
        """
        
        let activityVC = UIActivityViewController(
            activityItems: [shareText],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
}

// MARK: - 搜索栏
struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索健康格言...", text: $text)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !text.isEmpty {
                Button(action: {
                    text = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .cornerRadius(10)
    }
}

// MARK: - 分类选择器
struct CategorySelector: View {
    @Binding var selectedCategory: WisdomCategory?
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // 全部分类
                CategoryButton(
                    title: "全部",
                    isSelected: selectedCategory == nil
                ) {
                    selectedCategory = nil
                }
                
                // 各个分类
                ForEach(WisdomCategory.allCases, id: \.self) { category in
                    CategoryButton(
                        title: category.rawValue,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = selectedCategory == category ? nil : category
                    }
                }
            }
            .padding(.horizontal)
        }
    }
}

// MARK: - 分类按钮
struct CategoryButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(isSelected ? Color.blue : Color(.systemGray5))
                .foregroundColor(isSelected ? .white : .primary)
                .cornerRadius(20)
        }
    }
}

// MARK: - 格言卡片
struct WisdomCard: View {
    let wisdom: HealthWisdom
    @EnvironmentObject var healthWisdomService: HealthWisdomService
    @State private var showingLanguages = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 分类标签
            HStack {
                Text(wisdom.category.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .foregroundColor(.blue)
                    .cornerRadius(8)
                
                Spacer()
                
                Button(action: {
                    showingLanguages.toggle()
                }) {
                    Image(systemName: "globe")
                        .foregroundColor(.blue)
                }
            }
            
            // 主要内容
            VStack(alignment: .leading, spacing: 12) {
                // 维吾尔语
                Text(wisdom.uyghur)
                    .font(.title3)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.leading)
                    .foregroundColor(.primary)
                
                // 中文
                Text(wisdom.chinese)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .italic()
                
                if showingLanguages {
                    // 英文
                    Text(wisdom.english)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .italic()
                    
                    // 关键词
                    if !wisdom.keywords.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("相关关键词:")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                                ForEach(wisdom.keywords, id: \.self) { keyword in
                                    Text(keyword)
                                        .font(.caption)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(Color(.systemGray6))
                                        .cornerRadius(6)
                                }
                            }
                        }
                    }
                }
            }
            
            // 操作按钮
            HStack {
                Button(action: {
                    // 收藏功能
                    favoriteWisdom()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "heart")
                        Text("收藏")
                    }
                    .font(.caption)
                    .foregroundColor(.red)
                }
                
                Spacer()
                
                Button(action: {
                    // 分享功能
                    shareWisdom()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "square.and.arrow.up")
                        Text("分享")
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                
                Spacer()
                
                Button(action: {
                    // 设为今日格言
                    setAsDailyWisdom()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "star")
                        Text("设为今日")
                    }
                    .font(.caption)
                    .foregroundColor(.orange)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private func favoriteWisdom() {
        // 实现收藏功能
        // 这里可以保存到用户偏好或本地存储
    }
    
    private func shareWisdom() {
        let shareText = """
        健康格言 / ساغلاملىق ھېكمىتى
        
        \(wisdom.uyghur)
        
        \(wisdom.chinese)
        
        \(wisdom.english)
        
        —— 智能健康助手
        """
        
        let activityVC = UIActivityViewController(
            activityItems: [shareText],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
    
    private func setAsDailyWisdom() {
        // 设置为今日格言
        healthWisdomService.dailyWisdom = wisdom
    }
}

#Preview {
    WisdomDetailView()
        .environmentObject(HealthWisdomService())
}
