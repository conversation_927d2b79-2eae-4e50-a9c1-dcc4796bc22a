//
//  AddHealthDataView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct AddHealthDataView: View {
    @EnvironmentObject var healthDataService: HealthDataService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var selectedCategory: HealthCategory = .physical
    @State private var value: String = ""
    @State private var unit: String = ""
    @State private var notes: String = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("数据类型")) {
                    Picker("健康分类", selection: $selectedCategory) {
                        ForEach(HealthCategory.allCases, id: \.self) { category in
                            Label(category.displayName, systemImage: category.icon)
                                .tag(category)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                Section(header: Text("数据值")) {
                    HStack {
                        TextField("请输入数值", text: $value)
                            .keyboardType(.decimalPad)
                        
                        TextField("单位", text: $unit)
                            .frame(width: 80)
                    }
                    
                    // 常用单位建议
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(suggestedUnits, id: \.self) { suggestedUnit in
                                Button(suggestedUnit) {
                                    unit = suggestedUnit
                                }
                                .font(.caption)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color(.secondarySystemBackground))
                                .cornerRadius(12)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                
                Section(header: Text("备注（可选）")) {
                    TextField("添加备注信息", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
                
                Section {
                    Button("保存数据") {
                        saveHealthData()
                    }
                    .frame(maxWidth: .infinity)
                    .disabled(value.isEmpty || unit.isEmpty)
                }
            }
            .navigationTitle("添加健康数据")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private var suggestedUnits: [String] {
        switch selectedCategory {
        case .physical:
            return ["kg", "cm", "mmHg", "次/分", "°C"]
        case .mental:
            return ["分", "小时", "次"]
        case .lifestyle:
            return ["小时", "次", "杯"]
        case .nutrition:
            return ["g", "ml", "卡路里", "份"]
        case .sleep:
            return ["小时", "分钟", "次"]
        case .exercise:
            return ["分钟", "小时", "次", "公里"]
        case .work:
            return ["小时", "分钟", "次"]
        case .social:
            return ["次", "小时", "人"]
        case .environment:
            return ["分", "小时", "次"]
        case .preventive:
            return ["次", "天", "月"]
        }
    }
    
    private func saveHealthData() {
        guard let numericValue = Double(value) else {
            alertMessage = "请输入有效的数值"
            showingAlert = true
            return
        }
        
        guard !unit.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            alertMessage = "请输入单位"
            showingAlert = true
            return
        }
        
        let trimmedNotes = notes.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalNotes = trimmedNotes.isEmpty ? nil : trimmedNotes
        
        healthDataService.addHealthData(
            category: selectedCategory,
            value: numericValue,
            unit: unit.trimmingCharacters(in: .whitespacesAndNewlines),
            notes: finalNotes
        )
        
        presentationMode.wrappedValue.dismiss()
    }
}

#Preview {
    AddHealthDataView()
        .environmentObject(HealthDataService())
}
