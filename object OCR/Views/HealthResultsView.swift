//
//  HealthResultsView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct HealthResultsView: View {
    @EnvironmentObject var assessmentService: HealthAssessmentService
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                if let assessment = assessmentService.latestAssessment {
                    VStack(spacing: 24) {
                        // 总体评分
                        OverallScoreCard(assessment: assessment)
                        
                        // 分类得分
                        CategoryScoresCard(assessment: assessment)
                        
                        // 健康建议
                        RecommendationsCard(assessment: assessment)
                    }
                    .padding()
                } else {
                    VStack(spacing: 20) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 60))
                            .foregroundColor(.orange)
                        
                        Text("暂无评估结果")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text("请先完成健康评估问卷")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }
            .navigationTitle("评估结果")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .background(Color(.systemGroupedBackground))
        }
    }
}

struct OverallScoreCard: View {
    let assessment: HealthAssessment
    
    var body: some View {
        VStack(spacing: 20) {
            // 风险等级图标
            Image(systemName: riskLevelIcon(assessment.overallRiskLevel))
                .font(.system(size: 80))
                .foregroundColor(riskLevelColor(assessment.overallRiskLevel))
            
            // 总分
            VStack(spacing: 8) {
                Text("\(assessment.totalScore)")
                    .font(.system(size: 48, weight: .bold))
                    .foregroundColor(riskLevelColor(assessment.overallRiskLevel))
                
                Text("总分")
                    .font(.headline)
                    .foregroundColor(.secondary)
            }
            
            // 风险等级
            VStack(spacing: 4) {
                Text("健康风险等级")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(assessment.overallRiskLevel.displayName)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(riskLevelColor(assessment.overallRiskLevel))
            }
            
            // 评估时间
            Text("评估时间：\(formatDate(assessment.createdAt))")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func riskLevelColor(_ riskLevel: RiskLevel) -> Color {
        switch riskLevel {
        case .low: return .green
        case .moderate: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
    
    private func riskLevelIcon(_ riskLevel: RiskLevel) -> String {
        switch riskLevel {
        case .low: return "checkmark.shield.fill"
        case .moderate: return "exclamationmark.shield.fill"
        case .high: return "exclamationmark.triangle.fill"
        case .critical: return "xmark.shield.fill"
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

struct CategoryScoresCard: View {
    let assessment: HealthAssessment
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("各维度得分")
                .font(.headline)
                .fontWeight(.semibold)
            
            let sortedScores = assessment.categoryScores.sorted { $0.value > $1.value }
            
            VStack(spacing: 12) {
                ForEach(sortedScores, id: \.key) { category, score in
                    CategoryScoreRow(category: category, score: score)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct CategoryScoreRow: View {
    let category: HealthCategory
    let score: Int
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Label(category.displayName, systemImage: category.icon)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text("\(score)")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(scoreColor(score))
            }
            
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 6)
                        .cornerRadius(3)
                    
                    Rectangle()
                        .fill(scoreColor(score))
                        .frame(width: geometry.size.width * CGFloat(score) / 15.0, height: 6)
                        .cornerRadius(3)
                }
            }
            .frame(height: 6)
        }
    }
    
    private func scoreColor(_ score: Int) -> Color {
        switch score {
        case 12...15: return .green
        case 8...11: return .yellow
        case 4...7: return .orange
        default: return .red
        }
    }
}

struct RecommendationsCard: View {
    let assessment: HealthAssessment
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("健康建议")
                .font(.headline)
                .fontWeight(.semibold)
            
            if assessment.recommendations.isEmpty {
                Text("暂无特别建议，请保持良好的生活习惯")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(Color(.secondarySystemBackground))
                    .cornerRadius(8)
            } else {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(Array(assessment.recommendations.enumerated()), id: \.offset) { index, recommendation in
                        RecommendationRow(index: index + 1, recommendation: recommendation)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct RecommendationRow: View {
    let index: Int
    let recommendation: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text("\(index)")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(width: 20, height: 20)
                .background(Color.blue)
                .clipShape(Circle())
            
            Text(recommendation)
                .font(.subheadline)
                .multilineTextAlignment(.leading)
                .fixedSize(horizontal: false, vertical: true)
            
            Spacer()
        }
    }
}

#Preview {
    HealthResultsView()
        .environmentObject(HealthAssessmentService())
}
