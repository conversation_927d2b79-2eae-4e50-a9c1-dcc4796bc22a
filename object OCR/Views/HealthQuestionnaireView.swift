//
//  HealthQuestionnaireView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct HealthQuestionnaireView: View {
    @EnvironmentObject var questionService: HealthQuestionService
    @EnvironmentObject var assessmentService: HealthAssessmentService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var showingResults = false
    @State private var selectedOptions: Set<UUID> = []
    @State private var scaleValue: Double = 5.0
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if questionService.isCompleted {
                    // 完成页面
                    CompletionView()
                } else if let currentQuestion = questionService.currentQuestion {
                    // 问题页面
                    QuestionView(
                        question: currentQuestion,
                        selectedOptions: $selectedOptions,
                        scaleValue: $scaleValue
                    )
                } else {
                    // 加载页面
                    LoadingView()
                }
            }
            .navigationTitle("健康评估")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                if !questionService.isCompleted {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Text("\(questionService.currentQuestionIndex + 1)/\(questionService.questions.count)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .sheet(isPresented: $showingResults) {
            HealthResultsView()
        }
    }
}

// 问题视图
struct QuestionView: View {
    let question: HealthQuestion
    @Binding var selectedOptions: Set<UUID>
    @Binding var scaleValue: Double
    
    @EnvironmentObject var questionService: HealthQuestionService
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // 进度条
            ProgressView(value: Double(questionService.currentQuestionIndex), total: Double(questionService.questions.count))
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .padding()
            
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 分类标签
                    HStack {
                        Label(question.category.displayName, systemImage: question.category.icon)
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.blue)
                            .cornerRadius(12)
                        
                        Spacer()
                    }
                    
                    // 问题标题
                    VStack(alignment: .leading, spacing: 8) {
                        Text(question.title)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .multilineTextAlignment(.leading)
                        
                        if let description = question.description {
                            Text(description)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.leading)
                        }
                    }
                    
                    // 答案选项
                    switch question.type {
                    case .singleChoice:
                        SingleChoiceView(
                            options: question.options,
                            selectedOptions: $selectedOptions
                        )
                        
                    case .multipleChoice:
                        MultipleChoiceView(
                            options: question.options,
                            selectedOptions: $selectedOptions
                        )
                        
                    case .scale:
                        ScaleView(
                            minScore: question.minScore ?? 1,
                            maxScore: question.maxScore ?? 10,
                            scaleValue: $scaleValue
                        )
                        
                    case .yesNo:
                        YesNoView(
                            options: question.options,
                            selectedOptions: $selectedOptions
                        )
                    }
                    
                    Spacer(minLength: 100)
                }
                .padding()
            }
            
            // 底部按钮
            VStack(spacing: 12) {
                Divider()
                
                HStack(spacing: 16) {
                    // 上一题按钮
                    if questionService.currentQuestionIndex > 0 {
                        Button("上一题") {
                            previousQuestion()
                        }
                        .buttonStyle(SecondaryButtonStyle())
                        .frame(maxWidth: .infinity)
                    }
                    
                    // 下一题/完成按钮
                    Button(isLastQuestion ? "完成评估" : "下一题") {
                        nextQuestion()
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    .frame(maxWidth: .infinity)
                    .disabled(!isAnswerValid)
                }
                .padding()
            }
            .background(Color(.systemBackground))
        }
        .onAppear {
            resetAnswers()
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private var isLastQuestion: Bool {
        questionService.currentQuestionIndex == questionService.questions.count - 1
    }
    
    private var isAnswerValid: Bool {
        switch question.type {
        case .singleChoice, .multipleChoice, .yesNo:
            return !selectedOptions.isEmpty
        case .scale:
            return true // 量表总是有效的
        }
    }
    
    private func resetAnswers() {
        selectedOptions.removeAll()
        scaleValue = Double((question.minScore ?? 1) + (question.maxScore ?? 10)) / 2.0
    }
    
    private func nextQuestion() {
        guard isAnswerValid else {
            alertMessage = "请选择答案后再继续"
            showingAlert = true
            return
        }
        
        // 保存答案
        let selectedOptionIds = Array(selectedOptions)
        let scaleVal = question.type == .scale ? scaleValue : nil
        
        questionService.answerQuestion(
            selectedOptionIds: selectedOptionIds,
            scaleValue: scaleVal
        )
        
        // 重置答案状态
        resetAnswers()
    }
    
    private func previousQuestion() {
        questionService.previousQuestion()
        resetAnswers()
    }
}

// 单选视图
struct SingleChoiceView: View {
    let options: [AnswerOption]
    @Binding var selectedOptions: Set<UUID>
    
    var body: some View {
        VStack(spacing: 12) {
            ForEach(options) { option in
                OptionButton(
                    option: option,
                    isSelected: selectedOptions.contains(option.id),
                    selectionType: .single
                ) {
                    selectedOptions.removeAll()
                    selectedOptions.insert(option.id)
                }
            }
        }
    }
}

// 多选视图
struct MultipleChoiceView: View {
    let options: [AnswerOption]
    @Binding var selectedOptions: Set<UUID>
    
    var body: some View {
        VStack(spacing: 12) {
            ForEach(options) { option in
                OptionButton(
                    option: option,
                    isSelected: selectedOptions.contains(option.id),
                    selectionType: .multiple
                ) {
                    if selectedOptions.contains(option.id) {
                        selectedOptions.remove(option.id)
                    } else {
                        selectedOptions.insert(option.id)
                    }
                }
            }
        }
    }
}

// 量表视图
struct ScaleView: View {
    let minScore: Int
    let maxScore: Int
    @Binding var scaleValue: Double
    
    var body: some View {
        VStack(spacing: 20) {
            VStack(spacing: 8) {
                Text("当前值：\(Int(scaleValue))")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("请拖动滑块选择")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            VStack(spacing: 12) {
                Slider(
                    value: $scaleValue,
                    in: Double(minScore)...Double(maxScore),
                    step: 1.0
                )
                .accentColor(.blue)
                
                HStack {
                    Text("\(minScore)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(maxScore)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(Color(.secondarySystemBackground))
            .cornerRadius(12)
        }
    }
}

// 是非题视图
struct YesNoView: View {
    let options: [AnswerOption]
    @Binding var selectedOptions: Set<UUID>
    
    var body: some View {
        HStack(spacing: 16) {
            ForEach(options) { option in
                OptionButton(
                    option: option,
                    isSelected: selectedOptions.contains(option.id),
                    selectionType: .single
                ) {
                    selectedOptions.removeAll()
                    selectedOptions.insert(option.id)
                }
            }
        }
    }
}

// 选项按钮
struct OptionButton: View {
    let option: AnswerOption
    let isSelected: Bool
    let selectionType: SelectionType
    let action: () -> Void
    
    enum SelectionType {
        case single, multiple
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 选择指示器
                Image(systemName: selectionIndicator)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : .gray)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(option.text)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(isSelected ? .white : .primary)
                        .multilineTextAlignment(.leading)
                    
                    if let description = option.description {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
                            .multilineTextAlignment(.leading)
                    }
                }
                
                Spacer()
            }
            .padding()
            .background(isSelected ? Color.blue : Color(.secondarySystemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var selectionIndicator: String {
        switch selectionType {
        case .single:
            return isSelected ? "checkmark.circle.fill" : "circle"
        case .multiple:
            return isSelected ? "checkmark.square.fill" : "square"
        }
    }
}

// 完成视图
struct CompletionView: View {
    @EnvironmentObject var questionService: HealthQuestionService
    @EnvironmentObject var assessmentService: HealthAssessmentService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var showingResults = false
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            // 完成图标
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            // 完成信息
            VStack(spacing: 16) {
                Text("评估完成！")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("感谢您完成健康评估问卷\n我们将为您生成个性化的健康报告")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
            
            // 操作按钮
            VStack(spacing: 12) {
                Button("查看结果") {
                    let assessment = assessmentService.generateAssessment(from: questionService)
                    showingResults = true
                }
                .buttonStyle(PrimaryButtonStyle())
                
                Button("返回主页") {
                    questionService.resetQuestionnaire()
                    presentationMode.wrappedValue.dismiss()
                }
                .buttonStyle(SecondaryButtonStyle())
            }
            .padding()
        }
        .padding()
        .sheet(isPresented: $showingResults) {
            HealthResultsView()
        }
    }
}

// 加载视图
struct LoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("正在加载问题...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// 按钮样式
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.blue)
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .foregroundColor(.blue)
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color(.secondarySystemBackground))
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    HealthQuestionnaireView()
        .environmentObject(HealthQuestionService())
        .environmentObject(HealthAssessmentService())
}
