//
//  UserProfileView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct UserProfileView: View {
    @EnvironmentObject var userProfileService: UserProfileService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var name: String = ""
    @State private var age: String = ""
    @State private var selectedGender: Gender = .male
    @State private var height: String = ""
    @State private var weight: String = ""
    @State private var medicalHistory: String = ""
    @State private var allergies: String = ""
    @State private var medications: String = ""
    @State private var emergencyContact: String = ""
    @State private var emergencyPhone: String = ""
    
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("基本信息")) {
                    TextField("姓名", text: $name)
                    
                    TextField("年龄", text: $age)
                        .keyboardType(.numberPad)
                    
                    Picker("性别", selection: $selectedGender) {
                        ForEach(Gender.allCases, id: \.self) { gender in
                            Text(gender.displayName).tag(gender)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                Section(header: Text("身体指标")) {
                    HStack {
                        TextField("身高", text: $height)
                            .keyboardType(.decimalPad)
                        Text("cm")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        TextField("体重", text: $weight)
                            .keyboardType(.decimalPad)
                        Text("kg")
                            .foregroundColor(.secondary)
                    }
                    
                    if let bmi = calculateBMI() {
                        HStack {
                            Text("BMI")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(String(format: "%.1f", bmi))
                                .fontWeight(.medium)
                            Text("(\(getBMICategory(bmi)))")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Section(header: Text("健康信息")) {
                    TextField("既往病史", text: $medicalHistory, axis: .vertical)
                        .lineLimit(2...4)
                    
                    TextField("过敏史", text: $allergies, axis: .vertical)
                        .lineLimit(2...4)
                    
                    TextField("正在服用的药物", text: $medications, axis: .vertical)
                        .lineLimit(2...4)
                }
                
                Section(header: Text("紧急联系人")) {
                    TextField("联系人姓名", text: $emergencyContact)
                    
                    TextField("联系电话", text: $emergencyPhone)
                        .keyboardType(.phonePad)
                }
                
                Section {
                    Button("保存资料") {
                        saveProfile()
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .navigationTitle("个人资料")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .onAppear {
            loadCurrentProfile()
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func loadCurrentProfile() {
        if let profile = userProfileService.userProfile {
            name = profile.name ?? ""
            age = profile.age.map(String.init) ?? ""
            selectedGender = profile.gender ?? .male
            height = profile.height.map(String.init) ?? ""
            weight = profile.weight.map(String.init) ?? ""
            medicalHistory = profile.medicalHistory ?? ""
            allergies = profile.allergies ?? ""
            medications = profile.medications ?? ""
            emergencyContact = profile.emergencyContact ?? ""
            emergencyPhone = profile.emergencyPhone ?? ""
        }
    }
    
    private func calculateBMI() -> Double? {
        guard let heightValue = Double(height),
              let weightValue = Double(weight),
              heightValue > 0 else { return nil }
        
        let heightInMeters = heightValue / 100
        return weightValue / (heightInMeters * heightInMeters)
    }
    
    private func getBMICategory(_ bmi: Double) -> String {
        switch bmi {
        case ..<18.5: return "偏瘦"
        case 18.5..<24: return "正常"
        case 24..<28: return "超重"
        default: return "肥胖"
        }
    }
    
    private func saveProfile() {
        // 验证必填字段
        guard !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            alertMessage = "请输入姓名"
            showingAlert = true
            return
        }
        
        // 验证年龄
        var ageValue: Int? = nil
        if !age.isEmpty {
            guard let parsedAge = Int(age), parsedAge > 0, parsedAge < 150 else {
                alertMessage = "请输入有效的年龄"
                showingAlert = true
                return
            }
            ageValue = parsedAge
        }
        
        // 验证身高体重
        var heightValue: Double? = nil
        var weightValue: Double? = nil
        
        if !height.isEmpty {
            guard let parsedHeight = Double(height), parsedHeight > 0, parsedHeight < 300 else {
                alertMessage = "请输入有效的身高"
                showingAlert = true
                return
            }
            heightValue = parsedHeight
        }
        
        if !weight.isEmpty {
            guard let parsedWeight = Double(weight), parsedWeight > 0, parsedWeight < 500 else {
                alertMessage = "请输入有效的体重"
                showingAlert = true
                return
            }
            weightValue = parsedWeight
        }
        
        // 创建或更新用户档案
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedMedicalHistory = medicalHistory.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedAllergies = allergies.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedMedications = medications.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedEmergencyContact = emergencyContact.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedEmergencyPhone = emergencyPhone.trimmingCharacters(in: .whitespacesAndNewlines)
        
        userProfileService.updateProfile(
            name: trimmedName,
            age: ageValue,
            gender: selectedGender,
            height: heightValue,
            weight: weightValue,
            medicalHistory: trimmedMedicalHistory.isEmpty ? nil : trimmedMedicalHistory,
            allergies: trimmedAllergies.isEmpty ? nil : trimmedAllergies,
            medications: trimmedMedications.isEmpty ? nil : trimmedMedications,
            emergencyContact: trimmedEmergencyContact.isEmpty ? nil : trimmedEmergencyContact,
            emergencyPhone: trimmedEmergencyPhone.isEmpty ? nil : trimmedEmergencyPhone
        )
        
        presentationMode.wrappedValue.dismiss()
    }
}

#Preview {
    UserProfileView()
        .environmentObject(UserProfileService())
}
