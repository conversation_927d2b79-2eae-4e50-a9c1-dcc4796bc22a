//
//  AIDigitalDoctorMainView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct AIDigitalDoctorMainView: View {
    @EnvironmentObject var aiDoctorService: AIDigitalDoctorService
    @EnvironmentObject var userProfileService: UserProfileService
    @EnvironmentObject var healthWisdomService: HealthWisdomService
    
    @State private var selectedTab = 0
    @State private var showingHealthAnalysis = false
    @State private var showingRealTimeMonitoring = false
    @State private var chatMessages: [ChatMessage] = []
    @State private var currentMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部状态栏
                AIStatusBar()
                
                // 主要内容区域
                TabView(selection: $selectedTab) {
                    // AI咨询
                    AIChatView(
                        messages: $chatMessages,
                        currentMessage: $currentMessage
                    )
                    .tabItem {
                        Image(systemName: "message.fill")
                        Text("AI咨询")
                    }
                    .tag(0)
                    
                    // 健康分析
                    HealthAnalysisView()
                    .tabItem {
                        Image(systemName: "chart.bar.doc.horizontal.fill")
                        Text("健康分析")
                    }
                    .tag(1)
                    
                    // 实时监控
                    RealTimeMonitoringView()
                    .tabItem {
                        Image(systemName: "waveform.path.ecg")
                        Text("实时监控")
                    }
                    .tag(2)
                    
                    // 个性化建议
                    PersonalizedRecommendationsView()
                    .tabItem {
                        Image(systemName: "lightbulb.fill")
                        Text("个性化建议")
                    }
                    .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                
                // 底部导航
                AITabBar(selectedTab: $selectedTab)
            }
            .navigationTitle("AI数字医生")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: {
                            showingHealthAnalysis = true
                        }) {
                            Label("开始健康分析", systemImage: "chart.bar.doc.horizontal")
                        }
                        
                        Button(action: {
                            showingRealTimeMonitoring = true
                        }) {
                            Label("实时健康监控", systemImage: "waveform.path.ecg")
                        }
                        
                        Button(action: {
                            // 生成健康报告
                        }) {
                            Label("生成健康报告", systemImage: "doc.text")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
        }
        .onAppear {
            setupInitialChat()
        }
        .sheet(isPresented: $showingHealthAnalysis) {
            HealthAnalysisDetailView()
        }
        .sheet(isPresented: $showingRealTimeMonitoring) {
            RealTimeMonitoringDetailView()
        }
    }
    
    private func setupInitialChat() {
        if chatMessages.isEmpty {
            let welcomeMessage = ChatMessage(
                id: UUID(),
                content: "您好！我是您的AI数字医生助手。我可以帮助您：\n\n• 分析您的健康状况\n• 提供个性化健康建议\n• 监控实时健康数据\n• 回答健康相关问题\n\n请告诉我您今天感觉如何？",
                isFromUser: false,
                timestamp: Date(),
                messageType: .welcome
            )
            chatMessages.append(welcomeMessage)
        }
    }
}

// MARK: - AI状态栏
struct AIStatusBar: View {
    @EnvironmentObject var aiDoctorService: AIDigitalDoctorService
    
    var body: some View {
        HStack {
            // AI状态指示器
            HStack(spacing: 8) {
                Circle()
                    .fill(aiDoctorService.isAnalyzing ? Color.orange : Color.green)
                    .frame(width: 8, height: 8)
                
                Text(aiDoctorService.isAnalyzing ? "AI分析中..." : "AI医生在线")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 当前时间
            Text(DateFormatter.timeFormatter.string(from: Date()))
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
}

// MARK: - AI聊天视图
struct AIChatView: View {
    @Binding var messages: [ChatMessage]
    @Binding var currentMessage: String
    @EnvironmentObject var aiDoctorService: AIDigitalDoctorService
    @EnvironmentObject var healthWisdomService: HealthWisdomService
    @EnvironmentObject var userProfileService: UserProfileService

    var body: some View {
        VStack(spacing: 0) {
            // 聊天消息列表
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 12) {
                        // 显示AI服务中的消息
                        ForEach(aiDoctorService.chatMessages) { message in
                            ChatMessageBubble(message: message)
                                .id(message.id)
                        }

                        // 显示正在输入指示器
                        if aiDoctorService.isTyping {
                            TypingIndicator()
                        }
                    }
                    .padding()
                }
                .onChange(of: aiDoctorService.chatMessages.count) { _ in
                    if let lastMessage = aiDoctorService.chatMessages.last {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }
                    }
                }
            }

            Divider()

            // 输入区域
            ChatInputView(
                currentMessage: $currentMessage,
                onSend: sendMessage
            )
        }
        .onAppear {
            // 初始化欢迎消息
            if aiDoctorService.chatMessages.isEmpty {
                addWelcomeMessage()
            }
        }
    }
    
    private func sendMessage() {
        guard !currentMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        let messageToProcess = currentMessage
        currentMessage = ""

        // 使用AI数字医生服务处理消息
        Task {
            await aiDoctorService.sendMessage(messageToProcess, userProfile: userProfileService.currentProfile)
        }
    }

    private func addWelcomeMessage() {
        let welcomeMessage = ChatMessage(
            id: UUID(),
            content: """
            您好！我是您的AI健康助手 🏥

            我可以帮助您：
            • 分析健康症状和风险
            • 提供个性化健康建议
            • 解答健康相关问题
            • 制定健康改善计划

            请告诉我您想了解什么，或者描述您的健康状况。

            ⚠️ 重要提醒：我的建议仅供参考，不能替代专业医疗诊断。如有紧急情况，请立即就医。
            """,
            isFromUser: false,
            timestamp: Date(),
            messageType: .text
        )

        aiDoctorService.chatMessages.append(welcomeMessage)
    }
    
    // 移除原有的generateAIResponse方法，因为现在使用AI服务
}

// MARK: - 正在输入指示器
struct TypingIndicator: View {
    @State private var animationOffset: CGFloat = 0

    var body: some View {
        HStack {
            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.gray)
                        .frame(width: 8, height: 8)
                        .offset(y: animationOffset)
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray5))
            .cornerRadius(20)

            Spacer()
        }
        .onAppear {
            animationOffset = -4
        }
    }
}

// 继续原有的generateAIResponse方法（如果需要保留）
extension AIChatView {
    private func generateAIResponse(for message: String) -> String {
        let lowercaseMessage = message.lowercased()
        
        if lowercaseMessage.contains("头痛") || lowercaseMessage.contains("头疼") {
            return "头痛可能有多种原因，包括压力、睡眠不足、脱水等。建议您：\n\n• 保证充足睡眠（7-9小时）\n• 多喝水，保持水分\n• 适当休息，避免过度用眼\n• 如果持续严重，请及时就医\n\n需要我为您推荐一些缓解头痛的方法吗？"
        } else if lowercaseMessage.contains("血压") {
            return "关于血压管理，我建议：\n\n• 定期监测血压\n• 减少钠盐摄入\n• 增加钾元素丰富的食物\n• 保持适量运动\n• 控制体重\n• 管理压力\n\n您最近的血压读数是多少？我可以帮您分析一下。"
        } else if lowercaseMessage.contains("运动") || lowercaseMessage.contains("锻炼") {
            return "运动对健康非常重要！根据您的情况，我推荐：\n\n• 每周至少150分钟中等强度运动\n• 结合有氧运动和力量训练\n• 从低强度开始，逐步增加\n• 选择您喜欢的运动方式\n\n您目前的运动习惯如何？我可以为您制定个性化的运动计划。"
        } else if lowercaseMessage.contains("饮食") || lowercaseMessage.contains("吃") {
            return "健康饮食的建议：\n\n• 多吃蔬菜水果\n• 选择全谷物食品\n• 适量优质蛋白质\n• 限制加工食品\n• 控制糖分和盐分\n• 保持规律进餐\n\n您有什么特殊的饮食需求或限制吗？"
        } else {
            return "感谢您的咨询。作为您的AI健康助手，我会根据您的具体情况提供个性化建议。\n\n如果您有具体的健康问题，请详细描述症状，我会为您提供更准确的建议。\n\n同时，请记住任何严重或持续的健康问题都应该咨询专业医生。"
        }
    }
}

// MARK: - 聊天消息气泡
struct ChatMessageBubble: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer(minLength: 50)
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(16)
                    
                    Text(DateFormatter.timeFormatter.string(from: message.timestamp))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            } else {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Image(systemName: "stethoscope")
                            .foregroundColor(.blue)
                            .font(.caption)
                        
                        Text("AI医生")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                    
                    Text(message.content)
                        .padding()
                        .background(Color(.systemGray5))
                        .foregroundColor(.primary)
                        .cornerRadius(16)
                    
                    Text(DateFormatter.timeFormatter.string(from: message.timestamp))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer(minLength: 50)
            }
        }
    }
}

// MARK: - 聊天输入视图
struct ChatInputView: View {
    @Binding var currentMessage: String
    let onSend: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            TextField("输入您的健康问题...", text: $currentMessage, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(1...4)
            
            Button(action: onSend) {
                Image(systemName: "paperplane.fill")
                    .foregroundColor(.white)
                    .padding(8)
                    .background(currentMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? Color.gray : Color.blue)
                    .cornerRadius(8)
            }
            .disabled(currentMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        }
        .padding()
    }
}

// MARK: - AI标签栏
struct AITabBar: View {
    @Binding var selectedTab: Int
    
    private let tabs = [
        (title: "AI咨询", icon: "message.fill"),
        (title: "健康分析", icon: "chart.bar.doc.horizontal.fill"),
        (title: "实时监控", icon: "waveform.path.ecg"),
        (title: "个性化建议", icon: "lightbulb.fill")
    ]
    
    var body: some View {
        HStack {
            ForEach(0..<tabs.count, id: \.self) { index in
                Button(action: {
                    selectedTab = index
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tabs[index].icon)
                            .font(.system(size: 16))
                        
                        Text(tabs[index].title)
                            .font(.caption2)
                    }
                    .foregroundColor(selectedTab == index ? .blue : .gray)
                    .frame(maxWidth: .infinity)
                }
            }
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(Color(.separator)),
            alignment: .top
        )
    }
}

// MARK: - 数据模型
struct ChatMessage: Identifiable, Codable {
    let id: UUID
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let messageType: MessageType
    
    enum MessageType: String, Codable {
        case text = "text"
        case welcome = "welcome"
        case recommendation = "recommendation"
        case analysis = "analysis"
    }
}

// MARK: - 扩展
extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}

#Preview {
    AIDigitalDoctorMainView()
        .environmentObject(AIDigitalDoctorService())
        .environmentObject(UserProfileService())
        .environmentObject(HealthWisdomService())
}
