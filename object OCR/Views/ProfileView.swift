//
//  ProfileView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var userProfileService: UserProfileService
    @EnvironmentObject var multiLanguageService: MultiLanguageService
    @State private var showingProfileEdit = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 用户信息卡片
                    UserInfoCard(showingProfileEdit: $showingProfileEdit)
                    
                    // 设置选项
                    SettingsCard()
                }
                .padding()
            }
            .navigationTitle("个人中心")
            .background(Color(.systemGroupedBackground))
        }
        .sheet(isPresented: $showingProfileEdit) {
            UserProfileView()
        }
    }
}

struct UserInfoCard: View {
    @EnvironmentObject var userProfileService: UserProfileService
    @Binding var showingProfileEdit: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            // 头像
            Image(systemName: "person.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.blue)
            
            // 用户信息
            if let profile = userProfileService.userProfile {
                VStack(spacing: 8) {
                    Text(profile.name ?? "未设置姓名")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 16) {
                        if let age = profile.age {
                            Label("\(age)岁", systemImage: "calendar")
                        }
                        
                        if let gender = profile.gender {
                            Label(gender.displayName, systemImage: "person")
                        }
                    }
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    
                    if let bmi = profile.bmi {
                        Text("BMI: \(String(format: "%.1f", bmi)) (\(profile.bmiCategory))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            } else {
                Text("请完善个人信息")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Button("编辑资料") {
                showingProfileEdit = true
            }
            .buttonStyle(PrimaryButtonStyle())
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct SettingsCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text("设置")
                .font(.headline)
                .fontWeight(.semibold)
                .padding(.horizontal)
                .padding(.top)
            
            VStack(spacing: 0) {
                LanguageSettingsRow()

                Divider()
                    .padding(.leading, 50)

                NavigationLink(destination: AutoSaveSettingsView()) {
                    SettingsRowContent(
                        title: "自动保存",
                        subtitle: "数据自动保存设置",
                        icon: "square.and.arrow.down"
                    )
                }

                Divider()
                    .padding(.leading, 50)

                NavigationLink(destination: FileManagerView()) {
                    SettingsRowContent(
                        title: "文件管理",
                        subtitle: "数据备份与恢复",
                        icon: "folder"
                    )
                }

                Divider()
                    .padding(.leading, 50)

                SettingsRow(
                    title: "数据管理",
                    subtitle: "管理健康数据",
                    icon: "chart.bar",
                    action: {}
                )

                Divider()
                    .padding(.leading, 50)
                
                SettingsRow(
                    title: "隐私设置",
                    subtitle: "隐私和安全",
                    icon: "lock",
                    action: {}
                )
                
                Divider()
                    .padding(.leading, 50)
                
                SettingsRow(
                    title: "通知设置",
                    subtitle: "管理通知偏好",
                    icon: "bell",
                    action: {}
                )
                
                Divider()
                    .padding(.leading, 50)
                
                SettingsRow(
                    title: "关于应用",
                    subtitle: "版本信息",
                    icon: "info.circle",
                    action: {}
                )
            }
            .padding(.bottom)
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct SettingsRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.blue)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 语言设置行

struct LanguageSettingsRow: View {
    @EnvironmentObject var multiLanguageService: MultiLanguageService
    @State private var showingLanguageSelector = false

    var body: some View {
        Button(action: {
            showingLanguageSelector = true
        }) {
            HStack(spacing: 16) {
                // 图标
                Image(systemName: "globe")
                    .font(.title3)
                    .foregroundColor(.blue)
                    .frame(width: 24, height: 24)

                // 内容
                VStack(alignment: .leading, spacing: 2) {
                    Text(multiLanguageService.localizedString(for: "select_language"))
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    HStack(spacing: 4) {
                        Text(multiLanguageService.currentLanguageFlag)
                        Text(multiLanguageService.currentLanguageDisplayName)
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }

                Spacer()

                // 箭头
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
            .padding(.vertical, 12)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorView()
        }
    }
}

// MARK: - 设置行内容组件

struct SettingsRowContent: View {
    let title: String
    let subtitle: String
    let icon: String

    var body: some View {
        HStack(spacing: 16) {
            // 图标
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)

            // 内容
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // 箭头
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal)
        .padding(.vertical, 12)
    }
}

#Preview {
    ProfileView()
        .environmentObject(UserProfileService())
        .environmentObject(MultiLanguageService())
}
