//
//  HealthReportDetailView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct HealthReportDetailView: View {
    let report: HealthReport
    @Environment(\.presentationMode) var presentationMode
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 报告标题
                    ReportHeaderView(report: report)
                    
                    // 健康摘要
                    ReportSectionView(
                        title: "健康摘要",
                        icon: "heart.fill",
                        color: .red,
                        content: report.summary
                    )
                    
                    // 评估分析
                    ReportSectionView(
                        title: "评估分析",
                        icon: "chart.bar.fill",
                        color: .blue,
                        content: report.assessmentAnalysis
                    )
                    
                    // 数据分析
                    ReportSectionView(
                        title: "数据分析",
                        icon: "chart.line.uptrend.xyaxis",
                        color: .green,
                        content: report.dataAnalysis
                    )
                    
                    // 目标分析
                    ReportSectionView(
                        title: "目标分析",
                        icon: "target",
                        color: .orange,
                        content: report.goalAnalysis
                    )
                    
                    // 健康建议
                    RecommendationsSection(recommendations: report.recommendations)
                }
                .padding()
            }
            .navigationTitle("健康报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("分享") {
                        showingShareSheet = true
                    }
                }
            }
            .background(Color(.systemGroupedBackground))
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateReportText()])
        }
    }
    
    private func generateReportText() -> String {
        var text = "=== \(report.title) ===\n"
        text += "生成时间：\(formatDate(report.generatedDate))\n"
        text += "报告周期：\(report.reportPeriod)\n\n"
        
        text += "【健康摘要】\n\(report.summary)\n\n"
        text += "【评估分析】\n\(report.assessmentAnalysis)\n\n"
        text += "【数据分析】\n\(report.dataAnalysis)\n\n"
        text += "【目标分析】\n\(report.goalAnalysis)\n\n"
        
        text += "【健康建议】\n"
        for (index, recommendation) in report.recommendations.enumerated() {
            text += "\(index + 1). \(recommendation)\n"
        }
        
        return text
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

struct ReportHeaderView: View {
    let report: HealthReport
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "doc.text.fill")
                    .font(.title)
                    .foregroundColor(.green)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(report.title)
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("生成时间：\(formatDate(report.generatedDate))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            Text("报告周期：\(report.reportPeriod)")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

struct ReportSectionView: View {
    let title: String
    let icon: String
    let color: Color
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            Text(content)
                .font(.subheadline)
                .lineSpacing(4)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct RecommendationsSection: View {
    let recommendations: [String]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 8) {
                Image(systemName: "lightbulb.fill")
                    .font(.title3)
                    .foregroundColor(.yellow)
                
                Text("健康建议")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            if recommendations.isEmpty {
                Text("暂无特别建议，请保持良好的生活习惯")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(Color(.secondarySystemBackground))
                    .cornerRadius(8)
            } else {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(Array(recommendations.enumerated()), id: \.offset) { index, recommendation in
                        RecommendationDetailRow(index: index + 1, recommendation: recommendation)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct RecommendationDetailRow: View {
    let index: Int
    let recommendation: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text("\(index)")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Color.blue)
                .clipShape(Circle())
            
            Text(recommendation)
                .font(.subheadline)
                .lineSpacing(2)
                .multilineTextAlignment(.leading)
                .fixedSize(horizontal: false, vertical: true)
            
            Spacer()
        }
    }
}

// 分享功能
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    let sampleReport = HealthReport(
        title: "综合健康报告",
        generatedDate: Date(),
        reportPeriod: "最近30天",
        summary: "用户档案：年龄25岁，性别男，BMI22.5（正常）\n\n最新健康评估：总分85分，风险等级为低风险",
        assessmentAnalysis: "总体健康评估良好，各项指标正常",
        dataAnalysis: "最近30天内记录了15条健康数据",
        goalAnalysis: "设定了3个健康目标，完成率66.7%",
        recommendations: [
            "保持规律的作息时间",
            "增加有氧运动",
            "注意饮食均衡"
        ]
    )
    
    HealthReportDetailView(report: sampleReport)
}
