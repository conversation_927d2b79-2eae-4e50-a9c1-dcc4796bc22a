//
//  FileManagerView.swift
//  object OCR
//
//  Created by apple on 2025/7/30.
//

import SwiftUI
import UniformTypeIdentifiers

struct FileManagerView: View {
    @EnvironmentObject var autoSaveService: AutoSaveService
    @EnvironmentObject var multiLanguageService: MultiLanguageService
    @State private var savedFiles: [SavedFileInfo] = []
    @State private var showingExportSheet = false
    @State private var showingImportSheet = false
    @State private var showingDeleteAlert = false
    @State private var fileToDelete: SavedFileInfo?
    @State private var isRefreshing = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 自动保存状态卡片
                AutoSaveStatusCard()
                    .padding()
                
                // 文件列表
                if savedFiles.isEmpty {
                    EmptyFileListView()
                } else {
                    FileListView(
                        files: savedFiles,
                        onDelete: { file in
                            fileToDelete = file
                            showingDeleteAlert = true
                        }
                    )
                }
                
                Spacer()
                
                // 底部操作按钮
                BottomActionButtons(
                    onExport: { showingExportSheet = true },
                    onImport: { showingImportSheet = true },
                    onRefresh: { refreshFileList() }
                )
                .padding()
            }
            .navigationTitle(multiLanguageService.localizedString(for: "file_manager"))
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { refreshFileList() }) {
                        Image(systemName: "arrow.clockwise")
                            .rotationEffect(.degrees(isRefreshing ? 360 : 0))
                            .animation(.linear(duration: 1).repeatCount(isRefreshing ? .max : 1, autoreverses: false), value: isRefreshing)
                    }
                }
            }
        }
        .onAppear {
            refreshFileList()
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportDataView()
        }
        .sheet(isPresented: $showingImportSheet) {
            ImportDataView()
        }
        .alert("删除文件", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let file = fileToDelete {
                    deleteFile(file)
                }
            }
        } message: {
            if let file = fileToDelete {
                Text("确定要删除 \(file.type.displayName) 吗？此操作无法撤销。")
            }
        }
    }
    
    private func refreshFileList() {
        isRefreshing = true
        
        DispatchQueue.global(qos: .userInitiated).async {
            let files = autoSaveService.getAllSavedFiles()
            
            DispatchQueue.main.async {
                self.savedFiles = files
                self.isRefreshing = false
            }
        }
    }
    
    private func deleteFile(_ file: SavedFileInfo) {
        Task {
            do {
                try await autoSaveService.deleteData(type: file.type)
                refreshFileList()
            } catch {
                print("删除文件失败: \(error)")
            }
        }
    }
}

// MARK: - 自动保存状态卡片

struct AutoSaveStatusCard: View {
    @EnvironmentObject var autoSaveService: AutoSaveService
    @EnvironmentObject var multiLanguageService: MultiLanguageService
    
    var body: some View {
        VStack(spacing: 16) {
            // 状态指示器
            HStack {
                Circle()
                    .fill(statusColor)
                    .frame(width: 12, height: 12)
                    .scaleEffect(autoSaveService.isSaving ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: autoSaveService.isSaving)
                
                Text(autoSaveService.saveStatus.message)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if autoSaveService.isSaving {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            // 最后保存时间
            if let lastSaveTime = autoSaveService.lastSaveTime {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.secondary)
                    
                    Text("最后保存: \(lastSaveTime, formatter: dateFormatter)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
            
            // 自动保存设置
            HStack {
                Toggle("自动保存", isOn: .constant(autoSaveService.isAutoSaveEnabled))
                    .disabled(true)
                
                Spacer()
                
                Text("间隔: \(Int(autoSaveService.saveInterval))秒")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var statusColor: Color {
        switch autoSaveService.saveStatus {
        case .idle:
            return .gray
        case .saving:
            return .blue
        case .success:
            return .green
        case .failed:
            return .red
        }
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .medium
        return formatter
    }
}

// MARK: - 空文件列表视图

struct EmptyFileListView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "folder")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("暂无保存的文件")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("应用会自动保存您的数据，保存的文件将显示在这里")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - 文件列表视图

struct FileListView: View {
    let files: [SavedFileInfo]
    let onDelete: (SavedFileInfo) -> Void
    
    var body: some View {
        List {
            ForEach(files) { file in
                FileRowView(file: file, onDelete: { onDelete(file) })
            }
        }
        .listStyle(PlainListStyle())
    }
}

// MARK: - 文件行视图

struct FileRowView: View {
    let file: SavedFileInfo
    let onDelete: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            // 文件类型图标
            Image(systemName: fileIcon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 32, height: 32)
            
            // 文件信息
            VStack(alignment: .leading, spacing: 4) {
                Text(file.type.displayName)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                HStack {
                    Text(file.formattedSize)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("•")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(file.formattedDate)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 删除按钮
            Button(action: onDelete) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 8)
    }
    
    private var fileIcon: String {
        switch file.type {
        case .userProfile:
            return "person.circle"
        case .healthData:
            return "heart.circle"
        case .aiChatHistory:
            return "message.circle"
        case .healthReports:
            return "doc.circle"
        case .settings:
            return "gear.circle"
        case .languagePreferences:
            return "globe.circle"
        }
    }
}

// MARK: - 底部操作按钮

struct BottomActionButtons: View {
    let onExport: () -> Void
    let onImport: () -> Void
    let onRefresh: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            Button(action: onExport) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                    Text("导出")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
            
            Button(action: onImport) {
                HStack {
                    Image(systemName: "square.and.arrow.down")
                    Text("导入")
                }
                .font(.headline)
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
}

// MARK: - 导出数据视图

struct ExportDataView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var autoSaveService: AutoSaveService
    @State private var isExporting = false
    @State private var exportMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "square.and.arrow.up.circle")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                
                Text("导出数据")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("将所有应用数据导出到文件，可用于备份或迁移到其他设备")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                if isExporting {
                    ProgressView("正在导出...")
                        .padding()
                }
                
                if !exportMessage.isEmpty {
                    Text(exportMessage)
                        .font(.body)
                        .foregroundColor(.green)
                        .padding()
                }
                
                Spacer()
                
                Button(action: exportData) {
                    Text("开始导出")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                }
                .disabled(isExporting)
                .padding(.horizontal)
            }
            .navigationTitle("导出数据")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
    
    private func exportData() {
        isExporting = true
        exportMessage = ""
        
        Task {
            do {
                // 这里应该实现实际的导出逻辑
                // 暂时模拟导出过程
                try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
                
                await MainActor.run {
                    exportMessage = "数据导出成功！"
                    isExporting = false
                }
            } catch {
                await MainActor.run {
                    exportMessage = "导出失败: \(error.localizedDescription)"
                    isExporting = false
                }
            }
        }
    }
}

// MARK: - 导入数据视图

struct ImportDataView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var autoSaveService: AutoSaveService
    @State private var isImporting = false
    @State private var importMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "square.and.arrow.down.circle")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
                
                Text("导入数据")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("从备份文件恢复应用数据。注意：这将覆盖当前的所有数据")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                if isImporting {
                    ProgressView("正在导入...")
                        .padding()
                }
                
                if !importMessage.isEmpty {
                    Text(importMessage)
                        .font(.body)
                        .foregroundColor(.green)
                        .padding()
                }
                
                Spacer()
                
                Button(action: importData) {
                    Text("选择备份文件")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(12)
                }
                .disabled(isImporting)
                .padding(.horizontal)
            }
            .navigationTitle("导入数据")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
    
    private func importData() {
        isImporting = true
        importMessage = ""
        
        Task {
            do {
                // 这里应该实现实际的导入逻辑
                // 暂时模拟导入过程
                try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
                
                await MainActor.run {
                    importMessage = "数据导入成功！"
                    isImporting = false
                }
            } catch {
                await MainActor.run {
                    importMessage = "导入失败: \(error.localizedDescription)"
                    isImporting = false
                }
            }
        }
    }
}

// MARK: - 预览

#Preview {
    FileManagerView()
        .environmentObject(AutoSaveService())
        .environmentObject(MultiLanguageService())
}
