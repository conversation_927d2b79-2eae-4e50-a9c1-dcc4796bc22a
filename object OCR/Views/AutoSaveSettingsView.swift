//
//  AutoSaveSettingsView.swift
//  object OCR
//
//  Created by apple on 2025/7/30.
//

import SwiftUI

struct AutoSaveSettingsView: View {
    @EnvironmentObject var autoSaveService: AutoSaveService
    @EnvironmentObject var multiLanguageService: MultiLanguageService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var isAutoSaveEnabled: Bool = true
    @State private var saveInterval: Double = 30.0
    @State private var showingSaveConfirmation = false
    
    var body: some View {
        NavigationView {
            Form {
                // 自动保存开关
                Section(header: Text("自动保存设置")) {
                    Toggle("启用自动保存", isOn: $isAutoSaveEnabled)
                        .onChange(of: isAutoSaveEnabled) { _ in
                            updateSettings()
                        }
                    
                    if isAutoSaveEnabled {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("保存间隔")
                                Spacer()
                                Text("\(Int(saveInterval))秒")
                                    .foregroundColor(.secondary)
                            }
                            
                            Slider(value: $saveInterval, in: 10...300, step: 10) {
                                Text("保存间隔")
                            }
                            .onChange(of: saveInterval) { _ in
                                updateSettings()
                            }
                            
                            Text("建议设置为30-60秒，过短的间隔可能影响性能")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                // 当前状态
                Section(header: Text("当前状态")) {
                    HStack {
                        Text("保存状态")
                        Spacer()
                        StatusIndicator(status: autoSaveService.saveStatus)
                    }
                    
                    if let lastSaveTime = autoSaveService.lastSaveTime {
                        HStack {
                            Text("最后保存")
                            Spacer()
                            Text(lastSaveTime, formatter: dateFormatter)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    HStack {
                        Text("下次保存")
                        Spacer()
                        if isAutoSaveEnabled {
                            Text("约\(Int(saveInterval))秒后")
                                .foregroundColor(.secondary)
                        } else {
                            Text("已禁用")
                                .foregroundColor(.orange)
                        }
                    }
                }
                
                // 手动操作
                Section(header: Text("手动操作")) {
                    Button(action: {
                        Task {
                            await autoSaveService.saveAllData()
                            showingSaveConfirmation = true
                        }
                    }) {
                        HStack {
                            Image(systemName: "square.and.arrow.down")
                                .foregroundColor(.blue)
                            Text("立即保存所有数据")
                            Spacer()
                            if autoSaveService.isSaving {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                        }
                    }
                    .disabled(autoSaveService.isSaving)
                    
                    NavigationLink(destination: FileManagerView()) {
                        HStack {
                            Image(systemName: "folder")
                                .foregroundColor(.blue)
                            Text("管理保存的文件")
                        }
                    }
                }
                
                // 存储信息
                Section(header: Text("存储信息")) {
                    StorageInfoView()
                }
                
                // 高级设置
                Section(header: Text("高级设置")) {
                    NavigationLink(destination: BackupRestoreView()) {
                        HStack {
                            Image(systemName: "arrow.clockwise.circle")
                                .foregroundColor(.blue)
                            Text("备份与恢复")
                        }
                    }
                    
                    Button(action: {
                        // 清除所有保存的数据
                    }) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                            Text("清除所有数据")
                                .foregroundColor(.red)
                        }
                    }
                }
            }
            .navigationTitle("自动保存设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .onAppear {
            loadCurrentSettings()
        }
        .alert("保存完成", isPresented: $showingSaveConfirmation) {
            Button("确定") { }
        } message: {
            Text("所有数据已成功保存")
        }
    }
    
    private func loadCurrentSettings() {
        isAutoSaveEnabled = autoSaveService.isAutoSaveEnabled
        saveInterval = autoSaveService.saveInterval
    }
    
    private func updateSettings() {
        autoSaveService.updateAutoSaveSettings(
            enabled: isAutoSaveEnabled,
            interval: saveInterval
        )
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .medium
        return formatter
    }
}

// MARK: - 状态指示器

struct StatusIndicator: View {
    let status: AutoSaveService.SaveStatus
    
    var body: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(statusColor)
                .frame(width: 8, height: 8)
            
            Text(status.message)
                .font(.caption)
                .foregroundColor(statusColor)
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .idle:
            return .gray
        case .saving:
            return .blue
        case .success:
            return .green
        case .failed:
            return .red
        }
    }
}

// MARK: - 存储信息视图

struct StorageInfoView: View {
    @EnvironmentObject var autoSaveService: AutoSaveService
    @State private var storageInfo: StorageInfo?
    
    var body: some View {
        Group {
            if let info = storageInfo {
                VStack(spacing: 8) {
                    HStack {
                        Text("已用空间")
                        Spacer()
                        Text(info.formattedUsedSpace)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("文件数量")
                        Spacer()
                        Text("\(info.fileCount)个")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("最大文件")
                        Spacer()
                        Text(info.formattedLargestFile)
                            .foregroundColor(.secondary)
                    }
                }
            } else {
                HStack {
                    Text("正在计算...")
                    Spacer()
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
        }
        .onAppear {
            calculateStorageInfo()
        }
    }
    
    private func calculateStorageInfo() {
        DispatchQueue.global(qos: .userInitiated).async {
            let files = autoSaveService.getAllSavedFiles()
            let totalSize = files.reduce(0) { $0 + $1.size }
            let largestFile = files.max { $0.size < $1.size }
            
            let info = StorageInfo(
                usedSpace: totalSize,
                fileCount: files.count,
                largestFileSize: largestFile?.size ?? 0
            )
            
            DispatchQueue.main.async {
                self.storageInfo = info
            }
        }
    }
}

// MARK: - 存储信息结构

struct StorageInfo {
    let usedSpace: Int64
    let fileCount: Int
    let largestFileSize: Int64
    
    var formattedUsedSpace: String {
        ByteCountFormatter.string(fromByteCount: usedSpace, countStyle: .file)
    }
    
    var formattedLargestFile: String {
        ByteCountFormatter.string(fromByteCount: largestFileSize, countStyle: .file)
    }
}

// MARK: - 备份恢复视图

struct BackupRestoreView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "arrow.clockwise.circle")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("备份与恢复")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("功能开发中...")
                .font(.body)
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .navigationTitle("备份与恢复")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - 预览

#Preview {
    AutoSaveSettingsView()
        .environmentObject(AutoSaveService())
        .environmentObject(MultiLanguageService())
}
