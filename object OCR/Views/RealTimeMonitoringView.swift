//
//  RealTimeMonitoringView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct RealTimeMonitoringView: View {
    @EnvironmentObject var aiDoctorService: AIDigitalDoctorService
    @State private var isMonitoring = false
    @State private var currentHealthData = RealTimeHealthData(timestamp: Date())
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 监控状态
                MonitoringStatusCard(isMonitoring: $isMonitoring)
                
                // 实时数据显示
                if isMonitoring {
                    RealTimeDataGrid(healthData: currentHealthData)
                    
                    // 即时建议
                    if !aiDoctorService.instantAdvices.isEmpty {
                        InstantAdviceSection()
                    }
                } else {
                    StartMonitoringPrompt(onStart: startMonitoring)
                }
            }
            .padding()
        }
        .navigationTitle("实时监控")
        .onAppear {
            if isMonitoring {
                startDataSimulation()
            }
        }
    }
    
    private func startMonitoring() {
        isMonitoring = true
        startDataSimulation()
    }
    
    private func startDataSimulation() {
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            updateSimulatedData()
        }
    }
    
    private func updateSimulatedData() {
        currentHealthData = RealTimeHealthData(
            timestamp: Date(),
            bloodPressure: BloodPressureReading(
                systolic: Int.random(in: 110...140),
                diastolic: Int.random(in: 70...90),
                timestamp: Date()
            ),
            heartRate: Int.random(in: 60...100),
            bloodSugar: Double.random(in: 4.0...8.0),
            oxygenSaturation: Double.random(in: 95...100),
            temperature: Double.random(in: 36.0...37.5),
            dailySteps: Int.random(in: 5000...15000),
            lastNightSleep: Double.random(in: 6.0...9.0),
            stressLevel: Int.random(in: 1...10),
            moodLevel: Int.random(in: 1...10)
        )
        
        // 生成即时建议
        let advice = aiDoctorService.provideRealTimeAdvice(for: currentHealthData)
        aiDoctorService.instantAdvices = advice
    }
}

// MARK: - 监控状态卡片
struct MonitoringStatusCard: View {
    @Binding var isMonitoring: Bool
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Circle()
                    .fill(isMonitoring ? Color.green : Color.gray)
                    .frame(width: 12, height: 12)
                    .scaleEffect(isMonitoring ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isMonitoring)
                
                Text(isMonitoring ? "实时监控中" : "监控已停止")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: {
                    isMonitoring.toggle()
                }) {
                    Text(isMonitoring ? "停止" : "开始")
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(isMonitoring ? Color.red : Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
            }
            
            if isMonitoring {
                Text("AI正在实时分析您的健康数据")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 实时数据网格
struct RealTimeDataGrid: View {
    let healthData: RealTimeHealthData
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            // 血压
            if let bp = healthData.bloodPressure {
                HealthDataCard(
                    title: "血压",
                    value: "\(bp.systolic)/\(bp.diastolic)",
                    unit: "mmHg",
                    icon: "heart.fill",
                    color: bloodPressureColor(systolic: bp.systolic, diastolic: bp.diastolic)
                )
            }
            
            // 心率
            if let heartRate = healthData.heartRate {
                HealthDataCard(
                    title: "心率",
                    value: "\(heartRate)",
                    unit: "bpm",
                    icon: "waveform.path.ecg",
                    color: heartRateColor(heartRate)
                )
            }
            
            // 血糖
            if let bloodSugar = healthData.bloodSugar {
                HealthDataCard(
                    title: "血糖",
                    value: String(format: "%.1f", bloodSugar),
                    unit: "mmol/L",
                    icon: "drop.fill",
                    color: bloodSugarColor(bloodSugar)
                )
            }
            
            // 血氧
            if let oxygen = healthData.oxygenSaturation {
                HealthDataCard(
                    title: "血氧",
                    value: String(format: "%.1f", oxygen),
                    unit: "%",
                    icon: "lungs.fill",
                    color: oxygenColor(oxygen)
                )
            }
            
            // 体温
            if let temperature = healthData.temperature {
                HealthDataCard(
                    title: "体温",
                    value: String(format: "%.1f", temperature),
                    unit: "°C",
                    icon: "thermometer",
                    color: temperatureColor(temperature)
                )
            }
            
            // 步数
            if let steps = healthData.dailySteps {
                HealthDataCard(
                    title: "今日步数",
                    value: "\(steps)",
                    unit: "步",
                    icon: "figure.walk",
                    color: stepsColor(steps)
                )
            }
            
            // 睡眠
            if let sleep = healthData.lastNightSleep {
                HealthDataCard(
                    title: "昨夜睡眠",
                    value: String(format: "%.1f", sleep),
                    unit: "小时",
                    icon: "moon.fill",
                    color: sleepColor(sleep)
                )
            }
            
            // 压力水平
            if let stress = healthData.stressLevel {
                HealthDataCard(
                    title: "压力水平",
                    value: "\(stress)",
                    unit: "/10",
                    icon: "brain.head.profile",
                    color: stressColor(stress)
                )
            }
        }
    }
    
    // 颜色判断函数
    private func bloodPressureColor(systolic: Int, diastolic: Int) -> Color {
        if systolic > 140 || diastolic > 90 { return .red }
        if systolic > 130 || diastolic > 80 { return .orange }
        return .green
    }
    
    private func heartRateColor(_ rate: Int) -> Color {
        if rate < 60 || rate > 100 { return .orange }
        return .green
    }
    
    private func bloodSugarColor(_ sugar: Double) -> Color {
        if sugar < 3.9 || sugar > 7.8 { return .red }
        if sugar < 4.4 || sugar > 6.1 { return .orange }
        return .green
    }
    
    private func oxygenColor(_ oxygen: Double) -> Color {
        if oxygen < 95 { return .red }
        if oxygen < 98 { return .orange }
        return .green
    }
    
    private func temperatureColor(_ temp: Double) -> Color {
        if temp < 36.0 || temp > 37.5 { return .orange }
        if temp > 38.0 { return .red }
        return .green
    }
    
    private func stepsColor(_ steps: Int) -> Color {
        if steps < 5000 { return .red }
        if steps < 8000 { return .orange }
        return .green
    }
    
    private func sleepColor(_ hours: Double) -> Color {
        if hours < 6 || hours > 9 { return .orange }
        return .green
    }
    
    private func stressColor(_ level: Int) -> Color {
        if level > 7 { return .red }
        if level > 5 { return .orange }
        return .green
    }
}

// MARK: - 健康数据卡片
struct HealthDataCard: View {
    let title: String
    let value: String
    let unit: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title3)
                
                Spacer()
                
                Circle()
                    .fill(color)
                    .frame(width: 8, height: 8)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack(alignment: .bottom, spacing: 2) {
                    Text(value)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(color)
                    
                    Text(unit)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 即时建议区域
struct InstantAdviceSection: View {
    @EnvironmentObject var aiDoctorService: AIDigitalDoctorService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                Text("即时建议")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            LazyVStack(spacing: 12) {
                ForEach(aiDoctorService.instantAdvices) { advice in
                    InstantAdviceCard(advice: advice)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 即时建议卡片
struct InstantAdviceCard: View {
    let advice: InstantAdvice
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: adviceIcon(advice.type))
                .foregroundColor(adviceColor(advice.type))
                .font(.title3)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(advice.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(advice.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                if !advice.action.isEmpty {
                    Text("建议: \(advice.action)")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .italic()
                }
            }
            
            Spacer()
            
            Text(advice.type.rawValue)
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(adviceColor(advice.type).opacity(0.2))
                .foregroundColor(adviceColor(advice.type))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
    
    private func adviceIcon(_ type: AdviceType) -> String {
        switch type {
        case .suggestion: return "lightbulb"
        case .warning: return "exclamationmark.triangle"
        case .urgent: return "exclamationmark.octagon"
        case .information: return "info.circle"
        }
    }
    
    private func adviceColor(_ type: AdviceType) -> Color {
        switch type {
        case .suggestion: return .blue
        case .warning: return .orange
        case .urgent: return .red
        case .information: return .gray
        }
    }
}

// MARK: - 开始监控提示
struct StartMonitoringPrompt: View {
    let onStart: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "waveform.path.ecg")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            VStack(spacing: 8) {
                Text("开始实时健康监控")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("我们将实时监控您的健康数据，并提供即时的健康建议")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: onStart) {
                HStack {
                    Image(systemName: "play.fill")
                    Text("开始监控")
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.blue)
                .cornerRadius(12)
            }
        }
        .padding(30)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

#Preview {
    RealTimeMonitoringView()
        .environmentObject(AIDigitalDoctorService())
}
