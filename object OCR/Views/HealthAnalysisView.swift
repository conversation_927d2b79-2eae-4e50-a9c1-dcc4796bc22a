//
//  HealthAnalysisView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct HealthAnalysisView: View {
    @EnvironmentObject var aiDoctorService: AIDigitalDoctorService
    @EnvironmentObject var userProfileService: UserProfileService
    
    @State private var isAnalyzing = false
    @State private var showingDetailedAnalysis = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 分析状态卡片
                AnalysisStatusCard(isAnalyzing: $isAnalyzing)
                
                // 健康评分概览
                if let analysis = aiDoctorService.lastAnalysisResult {
                    HealthScoreOverviewCard(analysis: analysis)
                    
                    // 风险评估
                    RiskAssessmentCard(riskAssessment: analysis.riskAssessment)
                    
                    // 生活方式分析
                    LifestyleAnalysisCard(lifestyleAnalysis: analysis.lifestyleAnalysis)
                    
                    // 详细分析按钮
                    Button(action: {
                        showingDetailedAnalysis = true
                    }) {
                        HStack {
                            Text("查看详细分析报告")
                                .fontWeight(.medium)
                            Image(systemName: "arrow.right")
                        }
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue)
                        .cornerRadius(12)
                    }
                } else {
                    // 开始分析提示
                    StartAnalysisPromptCard(onStartAnalysis: startHealthAnalysis)
                }
            }
            .padding()
        }
        .navigationTitle("健康分析")
        .sheet(isPresented: $showingDetailedAnalysis) {
            if let analysis = aiDoctorService.lastAnalysisResult {
                DetailedAnalysisView(analysis: analysis)
            }
        }
    }
    
    private func startHealthAnalysis() {
        isAnalyzing = true
        
        Task {
            // 模拟分析过程
            try? await Task.sleep(nanoseconds: 3_000_000_000) // 3秒
            
            await MainActor.run {
                // 这里应该调用真实的AI分析服务
                // aiDoctorService.analyzeUserHealth(profile: userProfile)
                isAnalyzing = false
            }
        }
    }
}

// MARK: - 分析状态卡片
struct AnalysisStatusCard: View {
    @Binding var isAnalyzing: Bool
    
    var body: some View {
        VStack(spacing: 12) {
            if isAnalyzing {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("AI正在分析您的健康数据...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Text("这可能需要几分钟时间，请耐心等待")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("AI健康分析系统已就绪")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 健康评分概览卡片
struct HealthScoreOverviewCard: View {
    let analysis: HealthAnalysisResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("健康评分概览")
                .font(.headline)
                .fontWeight(.semibold)
            
            // 总体评分
            HStack {
                VStack(alignment: .leading) {
                    Text("总体健康评分")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("\(Int(analysis.overallScore))")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(scoreColor(analysis.overallScore))
                }
                
                Spacer()
                
                CircularProgressView(
                    progress: analysis.overallScore / 100,
                    color: scoreColor(analysis.overallScore)
                )
                .frame(width: 80, height: 80)
            }
            
            // 各项评分
            VStack(spacing: 12) {
                ScoreRow(title: "心理健康", score: analysis.mentalHealthScore)
                ScoreRow(title: "营养状况", score: analysis.nutritionAnalysis.score)
                ScoreRow(title: "运动健身", score: analysis.fitnessAnalysis.score)
                ScoreRow(title: "生活方式", score: analysis.lifestyleAnalysis.score)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func scoreColor(_ score: Double) -> Color {
        switch score {
        case 80...100: return .green
        case 60...79: return .yellow
        case 40...59: return .orange
        default: return .red
        }
    }
}

// MARK: - 评分行
struct ScoreRow: View {
    let title: String
    let score: Double
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
            
            Spacer()
            
            Text("\(Int(score))")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(scoreColor(score))
        }
    }
    
    private func scoreColor(_ score: Double) -> Color {
        switch score {
        case 80...100: return .green
        case 60...79: return .yellow
        case 40...59: return .orange
        default: return .red
        }
    }
}

// MARK: - 风险评估卡片
struct RiskAssessmentCard: View {
    let riskAssessment: RiskAssessment
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                Text("风险评估")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            // 总体风险等级
            HStack {
                Text("总体风险等级:")
                    .font(.subheadline)
                
                Spacer()
                
                Text(riskAssessment.overallRiskLevel.rawValue)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(riskColor(riskAssessment.overallRiskLevel).opacity(0.2))
                    .foregroundColor(riskColor(riskAssessment.overallRiskLevel))
                    .cornerRadius(8)
            }
            
            // 具体风险项目
            if !riskAssessment.specificRisks.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("需要关注的风险:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(riskAssessment.specificRisks.prefix(3), id: \.condition) { risk in
                        RiskRow(risk: risk)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func riskColor(_ level: RiskLevel) -> Color {
        switch level {
        case .low: return .green
        case .moderate: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

// MARK: - 风险行
struct RiskRow: View {
    let risk: HealthRisk
    
    var body: some View {
        HStack {
            Circle()
                .fill(riskColor(risk.level))
                .frame(width: 8, height: 8)
            
            Text(risk.condition)
                .font(.caption)
            
            Spacer()
            
            Text("\(Int(risk.probability * 100))%")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func riskColor(_ level: RiskLevel) -> Color {
        switch level {
        case .low: return .green
        case .moderate: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

// MARK: - 生活方式分析卡片
struct LifestyleAnalysisCard: View {
    let lifestyleAnalysis: LifestyleAnalysis
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "house.fill")
                    .foregroundColor(.blue)
                Text("生活方式分析")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            // 生活方式评分
            HStack {
                Text("生活方式评分:")
                    .font(.subheadline)
                
                Spacer()
                
                Text("\(Int(lifestyleAnalysis.score))")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(scoreColor(lifestyleAnalysis.score))
            }
            
            // 主要问题
            if !lifestyleAnalysis.issues.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("需要改善的方面:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(lifestyleAnalysis.issues.prefix(3), id: \.description) { issue in
                        LifestyleIssueRow(issue: issue)
                    }
                }
            }
            
            // 优势
            if !lifestyleAnalysis.strengths.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("您的优势:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(lifestyleAnalysis.strengths.prefix(2), id: \.description) { strength in
                        LifestyleStrengthRow(strength: strength)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func scoreColor(_ score: Double) -> Color {
        switch score {
        case 80...100: return .green
        case 60...79: return .yellow
        case 40...59: return .orange
        default: return .red
        }
    }
}

// MARK: - 生活方式问题行
struct LifestyleIssueRow: View {
    let issue: LifestyleIssue
    
    var body: some View {
        HStack {
            Image(systemName: "exclamationmark.circle.fill")
                .foregroundColor(severityColor(issue.severity))
                .font(.caption)
            
            Text(issue.description)
                .font(.caption)
                .lineLimit(2)
            
            Spacer()
        }
    }
    
    private func severityColor(_ severity: IssueSeverity) -> Color {
        switch severity {
        case .low: return .yellow
        case .moderate: return .orange
        case .high: return .red
        case .critical: return .red
        }
    }
}

// MARK: - 生活方式优势行
struct LifestyleStrengthRow: View {
    let strength: LifestyleStrength
    
    var body: some View {
        HStack {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
                .font(.caption)
            
            Text(strength.description)
                .font(.caption)
                .lineLimit(2)
            
            Spacer()
        }
    }
}

// MARK: - 开始分析提示卡片
struct StartAnalysisPromptCard: View {
    let onStartAnalysis: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            VStack(spacing: 8) {
                Text("开始AI健康分析")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("我们将分析您的健康数据，为您提供个性化的健康建议和风险评估")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: onStartAnalysis) {
                HStack {
                    Image(systemName: "play.fill")
                    Text("开始分析")
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.blue)
                .cornerRadius(12)
            }
        }
        .padding(30)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// MARK: - 圆形进度视图
struct CircularProgressView: View {
    let progress: Double
    let color: Color
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.3), lineWidth: 8)
            
            Circle()
                .trim(from: 0, to: progress)
                .stroke(color, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: progress)
        }
    }
}

#Preview {
    HealthAnalysisView()
        .environmentObject(AIDigitalDoctorService())
        .environmentObject(UserProfileService())
}
