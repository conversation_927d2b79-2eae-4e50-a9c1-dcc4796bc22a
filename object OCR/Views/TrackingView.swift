//
//  TrackingView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct TrackingView: View {
    @EnvironmentObject var healthDataService: HealthDataService
    @State private var showingAddData = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 添加数据卡片
                    AddDataCard(showingAddData: $showingAddData)
                    
                    // 数据概览
                    DataOverviewCard()
                    
                    // 最近数据
                    RecentDataCard()
                }
                .padding()
            }
            .navigationTitle("数据追踪")
            .background(Color(.systemGroupedBackground))
        }
        .sheet(isPresented: $showingAddData) {
            AddHealthDataView()
        }
    }
}

struct AddDataCard: View {
    @Binding var showingAddData: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "plus.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("记录健康数据")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("记录您的健康指标，追踪健康变化")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("添加数据") {
                showingAddData = true
            }
            .buttonStyle(PrimaryButtonStyle())
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct DataOverviewCard: View {
    @EnvironmentObject var healthDataService: HealthDataService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("数据概览")
                .font(.headline)
                .fontWeight(.semibold)
            
            let statistics = healthDataService.getCategoryStatistics()
            let sortedStats = statistics.sorted { $0.value > $1.value }
            
            if sortedStats.allSatisfy({ $0.value == 0 }) {
                VStack(spacing: 12) {
                    Image(systemName: "chart.bar")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text("暂无数据记录")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(sortedStats.prefix(6), id: \.key) { category, count in
                        if count > 0 {
                            DataCategoryCard(category: category, count: count)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct DataCategoryCard: View {
    let category: HealthCategory
    let count: Int
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: category.icon)
                .font(.title2)
                .foregroundColor(.blue)
            
            Text("\(count)")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(category.displayName)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(8)
    }
}

struct RecentDataCard: View {
    @EnvironmentObject var healthDataService: HealthDataService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("最近记录")
                .font(.headline)
                .fontWeight(.semibold)
            
            let recentData = healthDataService.getRecentHealthData(days: 7)
            
            if recentData.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "clock")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text("最近7天无数据记录")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                ForEach(recentData.prefix(5)) { entry in
                    HealthDataRow(entry: entry)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct HealthDataRow: View {
    let entry: HealthDataEntry
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: entry.category.icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(entry.category.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("\(entry.value, specifier: "%.1f") \(entry.unit)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(formatTime(entry.recordedAt))
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

#Preview {
    TrackingView()
        .environmentObject(HealthDataService())
}
