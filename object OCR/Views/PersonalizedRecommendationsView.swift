//
//  PersonalizedRecommendationsView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct PersonalizedRecommendationsView: View {
    @EnvironmentObject var aiDoctorService: AIDigitalDoctorService
    @State private var selectedCategory: HealthCategory? = nil
    @State private var selectedPriority: RecommendationPriority? = nil
    @State private var showingFilters = false
    
    var filteredRecommendations: [HealthRecommendation] {
        var recommendations = aiDoctorService.currentRecommendations
        
        if let category = selectedCategory {
            recommendations = recommendations.filter { $0.category == category }
        }
        
        if let priority = selectedPriority {
            recommendations = recommendations.filter { $0.priority == priority }
        }
        
        return recommendations.sorted { $0.priority.rawValue < $1.priority.rawValue }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 筛选栏
            FilterBar(
                selectedCategory: $selectedCategory,
                selectedPriority: $selectedPriority,
                showingFilters: $showingFilters
            )
            
            if filteredRecommendations.isEmpty {
                EmptyRecommendationsView()
            } else {
                ScrollView {
                    LazyVStack(spacing: 16) {
                        ForEach(filteredRecommendations) { recommendation in
                            RecommendationCard(recommendation: recommendation)
                        }
                    }
                    .padding()
                }
            }
        }
        .navigationTitle("个性化建议")
        .onAppear {
            loadRecommendations()
        }
    }
    
    private func loadRecommendations() {
        // 模拟加载推荐数据
        if aiDoctorService.currentRecommendations.isEmpty {
            aiDoctorService.currentRecommendations = generateSampleRecommendations()
        }
    }
    
    private func generateSampleRecommendations() -> [HealthRecommendation] {
        return [
            HealthRecommendation(
                title: "增加有氧运动",
                description: "根据您的心血管健康评估，建议增加每周的有氧运动时间",
                category: .exercise,
                priority: .high,
                timeframe: .shortTerm,
                actionSteps: [
                    ActionStep(step: "每天快走30分钟", duration: "30分钟", frequency: "每天", difficulty: .easy),
                    ActionStep(step: "每周游泳2次", duration: "45分钟", frequency: "每周2次", difficulty: .moderate)
                ],
                expectedOutcome: "改善心血管健康，降低心脏病风险",
                trackingMetrics: ["心率", "血压", "运动时长"]
            ),
            HealthRecommendation(
                title: "优化睡眠质量",
                description: "您的睡眠质量有待改善，建议调整睡眠习惯",
                category: .sleep,
                priority: .medium,
                timeframe: .mediumTerm,
                actionSteps: [
                    ActionStep(step: "建立固定作息时间", duration: "持续", frequency: "每天", difficulty: .easy),
                    ActionStep(step: "睡前1小时避免电子设备", duration: "1小时", frequency: "每天", difficulty: .moderate)
                ],
                expectedOutcome: "提高睡眠质量，增强免疫力",
                trackingMetrics: ["睡眠时长", "睡眠质量评分"]
            ),
            HealthRecommendation(
                title: "减少糖分摄入",
                description: "您的血糖水平偏高，建议调整饮食结构",
                category: .nutrition,
                priority: .urgent,
                timeframe: .immediate,
                actionSteps: [
                    ActionStep(step: "避免含糖饮料", duration: "持续", frequency: "每天", difficulty: .easy),
                    ActionStep(step: "选择低GI食物", duration: "持续", frequency: "每餐", difficulty: .moderate)
                ],
                expectedOutcome: "控制血糖水平，预防糖尿病",
                trackingMetrics: ["血糖水平", "体重"]
            )
        ]
    }
}

// MARK: - 筛选栏
struct FilterBar: View {
    @Binding var selectedCategory: HealthCategory?
    @Binding var selectedPriority: RecommendationPriority?
    @Binding var showingFilters: Bool
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("筛选条件")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: {
                    withAnimation {
                        showingFilters.toggle()
                    }
                }) {
                    Image(systemName: showingFilters ? "chevron.up" : "chevron.down")
                        .foregroundColor(.blue)
                }
            }
            
            if showingFilters {
                VStack(spacing: 12) {
                    // 分类筛选
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            FilterButton(
                                title: "全部分类",
                                isSelected: selectedCategory == nil
                            ) {
                                selectedCategory = nil
                            }
                            
                            ForEach(HealthCategory.allCases, id: \.self) { category in
                                FilterButton(
                                    title: category.displayName,
                                    isSelected: selectedCategory == category
                                ) {
                                    selectedCategory = selectedCategory == category ? nil : category
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                    
                    // 优先级筛选
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            FilterButton(
                                title: "全部优先级",
                                isSelected: selectedPriority == nil
                            ) {
                                selectedPriority = nil
                            }
                            
                            ForEach(RecommendationPriority.allCases, id: \.self) { priority in
                                FilterButton(
                                    title: priority.displayName,
                                    isSelected: selectedPriority == priority
                                ) {
                                    selectedPriority = selectedPriority == priority ? nil : priority
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
}

// MARK: - 筛选按钮
struct FilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(isSelected ? Color.blue : Color(.systemBackground))
                .foregroundColor(isSelected ? .white : .primary)
                .cornerRadius(16)
        }
    }
}

// MARK: - 推荐卡片
struct RecommendationCard: View {
    let recommendation: HealthRecommendation
    @State private var isExpanded = false
    @State private var showingActionSteps = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 头部信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: recommendation.category.icon)
                            .foregroundColor(.blue)
                        
                        Text(recommendation.category.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text(recommendation.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                // 优先级标签
                Text(recommendation.priority.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(recommendation.priority.color.opacity(0.2))
                    .foregroundColor(recommendation.priority.color)
                    .cornerRadius(8)
            }
            
            // 描述
            Text(recommendation.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(isExpanded ? nil : 2)
            
            // 时间框架
            HStack {
                Image(systemName: "clock")
                    .foregroundColor(.orange)
                    .font(.caption)
                
                Text("时间框架: \(recommendation.timeframe.rawValue)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 预期结果
            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "target")
                            .foregroundColor(.green)
                            .font(.caption)
                        
                        Text("预期结果:")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    
                    Text(recommendation.expectedOutcome)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.leading, 16)
                }
            }
            
            // 操作按钮
            HStack(spacing: 12) {
                Button(action: {
                    withAnimation {
                        isExpanded.toggle()
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        Text(isExpanded ? "收起" : "展开")
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                
                Spacer()
                
                Button(action: {
                    showingActionSteps = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "list.bullet")
                        Text("查看步骤")
                    }
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue)
                    .cornerRadius(8)
                }
                
                Button(action: {
                    // 标记为已完成
                }) {
                    Image(systemName: "checkmark.circle")
                        .foregroundColor(.green)
                        .font(.title3)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        .sheet(isPresented: $showingActionSteps) {
            ActionStepsView(recommendation: recommendation)
        }
    }
}

// MARK: - 空状态视图
struct EmptyRecommendationsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "lightbulb")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("暂无个性化建议")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("完成健康评估后，我们将为您提供个性化的健康建议")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: {
                // 跳转到健康评估
            }) {
                HStack {
                    Image(systemName: "heart.text.square")
                    Text("开始健康评估")
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
        }
        .padding(30)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - 行动步骤视图
struct ActionStepsView: View {
    let recommendation: HealthRecommendation
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 推荐标题
                    VStack(alignment: .leading, spacing: 8) {
                        Text(recommendation.title)
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text(recommendation.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Divider()
                    
                    // 行动步骤
                    VStack(alignment: .leading, spacing: 16) {
                        Text("行动步骤")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        ForEach(Array(recommendation.actionSteps.enumerated()), id: \.offset) { index, step in
                            ActionStepRow(step: step, index: index + 1)
                        }
                    }
                    
                    Divider()
                    
                    // 追踪指标
                    if !recommendation.trackingMetrics.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("追踪指标")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                                ForEach(recommendation.trackingMetrics, id: \.self) { metric in
                                    Text(metric)
                                        .font(.caption)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(Color.blue.opacity(0.2))
                                        .foregroundColor(.blue)
                                        .cornerRadius(6)
                                }
                            }
                        }
                    }
                    
                    Divider()
                    
                    // 预期结果
                    VStack(alignment: .leading, spacing: 8) {
                        Text("预期结果")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text(recommendation.expectedOutcome)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
            }
            .navigationTitle("行动计划")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
}

// MARK: - 行动步骤行
struct ActionStepRow: View {
    let step: ActionStep
    let index: Int
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 步骤编号
            Text("\(index)")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Color.blue)
                .cornerRadius(12)
            
            VStack(alignment: .leading, spacing: 8) {
                Text(step.step)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack(spacing: 16) {
                    Label(step.duration, systemImage: "clock")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Label(step.frequency, systemImage: "repeat")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(step.difficulty.rawValue)
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(difficultyColor(step.difficulty).opacity(0.2))
                        .foregroundColor(difficultyColor(step.difficulty))
                        .cornerRadius(4)
                }
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
    
    private func difficultyColor(_ difficulty: DifficultyLevel) -> Color {
        switch difficulty {
        case .easy: return .green
        case .moderate: return .yellow
        case .hard: return .orange
        case .veryHard: return .red
        }
    }
}

#Preview {
    PersonalizedRecommendationsView()
        .environmentObject(AIDigitalDoctorService())
}
