//
//  AssessmentView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct AssessmentView: View {
    @EnvironmentObject var assessmentService: HealthAssessmentService
    @State private var showingQuestionnaire = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 开始评估卡片
                    StartAssessmentCard(showingQuestionnaire: $showingQuestionnaire)
                    
                    // 评估历史
                    AssessmentHistoryCard()
                }
                .padding()
            }
            .navigationTitle("健康评估")
            .background(Color(.systemGroupedBackground))
        }
        .sheet(isPresented: $showingQuestionnaire) {
            HealthQuestionnaireView()
        }
    }
}

struct StartAssessmentCard: View {
    @Binding var showingQuestionnaire: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "heart.text.square.fill")
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            Text("健康评估")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("通过科学的问卷调查，全面了解您的健康状况")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("开始评估") {
                showingQuestionnaire = true
            }
            .buttonStyle(PrimaryButtonStyle())
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct AssessmentHistoryCard: View {
    @EnvironmentObject var assessmentService: HealthAssessmentService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("评估历史")
                .font(.headline)
                .fontWeight(.semibold)
            
            if assessmentService.assessmentHistory.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "clock")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text("暂无评估记录")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                ForEach(assessmentService.assessmentHistory.prefix(5)) { assessment in
                    AssessmentHistoryRow(assessment: assessment)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct AssessmentHistoryRow: View {
    let assessment: HealthAssessment
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "heart.circle.fill")
                .font(.title3)
                .foregroundColor(riskLevelColor(assessment.overallRiskLevel))
            
            VStack(alignment: .leading, spacing: 2) {
                Text("健康评估")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("总分 \(assessment.totalScore) 分 · \(assessment.overallRiskLevel.displayName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(formatDate(assessment.createdAt))
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
    
    private func riskLevelColor(_ riskLevel: RiskLevel) -> Color {
        switch riskLevel {
        case .low: return .green
        case .moderate: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

#Preview {
    AssessmentView()
        .environmentObject(HealthAssessmentService())
}
