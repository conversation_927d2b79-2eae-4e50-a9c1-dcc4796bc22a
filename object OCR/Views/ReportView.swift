//
//  ReportView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct ReportView: View {
    @EnvironmentObject var reportService: HealthReportService
    @EnvironmentObject var assessmentService: HealthAssessmentService
    @EnvironmentObject var healthDataService: HealthDataService
    @EnvironmentObject var userProfileService: UserProfileService
    
    @State private var showingReportDetail = false
    @State private var selectedReport: HealthReport?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 生成报告卡片
                    GenerateReportCard()
                    
                    // 报告历史
                    ReportHistoryCard()
                }
                .padding()
            }
            .navigationTitle("健康报告")
            .background(Color(.systemGroupedBackground))
        }
        .sheet(item: $selectedReport) { report in
            HealthReportDetailView(report: report)
        }
    }
}

struct GenerateReportCard: View {
    @EnvironmentObject var reportService: HealthReportService
    @EnvironmentObject var assessmentService: HealthAssessmentService
    @EnvironmentObject var healthDataService: HealthDataService
    @EnvironmentObject var userProfileService: UserProfileService
    
    @State private var isGenerating = false
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)
            
            Text("生成健康报告")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("基于您的评估结果和健康数据生成综合报告")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("生成报告") {
                generateReport()
            }
            .buttonStyle(PrimaryButtonStyle())
            .disabled(isGenerating)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func generateReport() {
        isGenerating = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let _ = reportService.generateComprehensiveReport(
                assessmentService: assessmentService,
                dataService: healthDataService,
                profileService: userProfileService
            )
            isGenerating = false
        }
    }
}

struct ReportHistoryCard: View {
    @EnvironmentObject var reportService: HealthReportService
    @State private var selectedReport: HealthReport?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("报告历史")
                .font(.headline)
                .fontWeight(.semibold)
            
            if reportService.reports.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "doc")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text("暂无报告记录")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                ForEach(reportService.reports) { report in
                    ReportHistoryRow(report: report) {
                        selectedReport = report
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        .sheet(item: $selectedReport) { report in
            HealthReportDetailView(report: report)
        }
    }
}

struct ReportHistoryRow: View {
    let report: HealthReport
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: "doc.text.fill")
                    .font(.title3)
                    .foregroundColor(.green)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(report.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(report.reportPeriod)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text(formatDate(report.generatedDate))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

#Preview {
    ReportView()
        .environmentObject(HealthReportService())
        .environmentObject(HealthAssessmentService())
        .environmentObject(HealthDataService())
        .environmentObject(UserProfileService())
}
