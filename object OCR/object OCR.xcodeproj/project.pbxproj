// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1000001 /* App.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000000 /* App.swift */; };
		A1000003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000002 /* ContentView.swift */; };
		A1000005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000004 /* Assets.xcassets */; };
		A1000010 /* HealthQuestion.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100000F /* HealthQuestion.swift */; };
		A1000011 /* AIHealthModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000010 /* AIHealthModels.swift */; };
		A1000012 /* ExtendedHealthModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000011 /* ExtendedHealthModels.swift */; };
		A1000020 /* HealthQuestionService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100001F /* HealthQuestionService.swift */; };
		A1000021 /* HealthAssessmentService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000020 /* HealthAssessmentService.swift */; };
		A1000022 /* UserProfileService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000021 /* UserProfileService.swift */; };
		A1000023 /* HealthDataService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000022 /* HealthDataService.swift */; };
		A1000024 /* HealthReportService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000023 /* HealthReportService.swift */; };
		A1000025 /* HealthWisdomService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000024 /* HealthWisdomService.swift */; };
		A1000026 /* AIDigitalDoctorService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000025 /* AIDigitalDoctorService.swift */; };
		A1000027 /* AdvancedHealthAnalysisService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000026 /* AdvancedHealthAnalysisService.swift */; };
		A1000028 /* HealthTrendAnalysisService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000027 /* HealthTrendAnalysisService.swift */; };
		A1000029 /* PersonalizedRecommendationEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000028 /* PersonalizedRecommendationEngine.swift */; };
		A100002A /* AIConversationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000029 /* AIConversationService.swift */; };
		A100002B /* MultiLanguageService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100002A /* MultiLanguageService.swift */; };
		A1000030 /* HealthQuestionnaireView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100002F /* HealthQuestionnaireView.swift */; };
		A1000031 /* AssessmentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000030 /* AssessmentView.swift */; };
		A1000032 /* UserProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000031 /* UserProfileView.swift */; };
		A1000033 /* TrackingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000032 /* TrackingView.swift */; };
		A1000034 /* AddHealthDataView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000033 /* AddHealthDataView.swift */; };
		A1000035 /* ReportView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000034 /* ReportView.swift */; };
		A1000036 /* HealthReportDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000035 /* HealthReportDetailView.swift */; };
		A1000037 /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000036 /* ProfileView.swift */; };
		A1000038 /* WisdomDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000037 /* WisdomDetailView.swift */; };
		A1000039 /* AIDigitalDoctorMainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000038 /* AIDigitalDoctorMainView.swift */; };
		A100003A /* HealthAnalysisView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000039 /* HealthAnalysisView.swift */; };
		A100003B /* RealTimeMonitoringView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100003A /* RealTimeMonitoringView.swift */; };
		A100003C /* PersonalizedRecommendationsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100003B /* PersonalizedRecommendationsView.swift */; };
		A100003D /* HealthResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100003C /* HealthResultsView.swift */; };
		A100003E /* LanguageSelectorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100003D /* LanguageSelectorView.swift */; };
		A100003F /* AutoSaveService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100003E /* AutoSaveService.swift */; };
		A1000040 /* FileManagerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100003F /* FileManagerView.swift */; };
		A1000041 /* AutoSaveSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000040 /* AutoSaveSettingsView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A0FFFFFF /* object OCR.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "object OCR.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000000 /* App.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = App.swift; sourceTree = "<group>"; };
		A1000002 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1000004 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A100000F /* HealthQuestion.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthQuestion.swift; sourceTree = "<group>"; };
		A1000010 /* AIHealthModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHealthModels.swift; sourceTree = "<group>"; };
		A1000011 /* ExtendedHealthModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExtendedHealthModels.swift; sourceTree = "<group>"; };
		A100001F /* HealthQuestionService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthQuestionService.swift; sourceTree = "<group>"; };
		A1000020 /* HealthAssessmentService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthAssessmentService.swift; sourceTree = "<group>"; };
		A1000021 /* UserProfileService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserProfileService.swift; sourceTree = "<group>"; };
		A1000022 /* HealthDataService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthDataService.swift; sourceTree = "<group>"; };
		A1000023 /* HealthReportService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthReportService.swift; sourceTree = "<group>"; };
		A1000024 /* HealthWisdomService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthWisdomService.swift; sourceTree = "<group>"; };
		A1000025 /* AIDigitalDoctorService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIDigitalDoctorService.swift; sourceTree = "<group>"; };
		A1000026 /* AdvancedHealthAnalysisService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvancedHealthAnalysisService.swift; sourceTree = "<group>"; };
		A1000027 /* HealthTrendAnalysisService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthTrendAnalysisService.swift; sourceTree = "<group>"; };
		A1000028 /* PersonalizedRecommendationEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersonalizedRecommendationEngine.swift; sourceTree = "<group>"; };
		A1000029 /* AIConversationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIConversationService.swift; sourceTree = "<group>"; };
		A100002A /* MultiLanguageService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiLanguageService.swift; sourceTree = "<group>"; };
		A100002F /* HealthQuestionnaireView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthQuestionnaireView.swift; sourceTree = "<group>"; };
		A1000030 /* AssessmentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AssessmentView.swift; sourceTree = "<group>"; };
		A1000031 /* UserProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserProfileView.swift; sourceTree = "<group>"; };
		A1000032 /* TrackingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrackingView.swift; sourceTree = "<group>"; };
		A1000033 /* AddHealthDataView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddHealthDataView.swift; sourceTree = "<group>"; };
		A1000034 /* ReportView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReportView.swift; sourceTree = "<group>"; };
		A1000035 /* HealthReportDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthReportDetailView.swift; sourceTree = "<group>"; };
		A1000036 /* ProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		A1000037 /* WisdomDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WisdomDetailView.swift; sourceTree = "<group>"; };
		A1000038 /* AIDigitalDoctorMainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIDigitalDoctorMainView.swift; sourceTree = "<group>"; };
		A1000039 /* HealthAnalysisView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthAnalysisView.swift; sourceTree = "<group>"; };
		A100003A /* RealTimeMonitoringView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RealTimeMonitoringView.swift; sourceTree = "<group>"; };
		A100003B /* PersonalizedRecommendationsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersonalizedRecommendationsView.swift; sourceTree = "<group>"; };
		A100003C /* HealthResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthResultsView.swift; sourceTree = "<group>"; };
		A100003D /* LanguageSelectorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageSelectorView.swift; sourceTree = "<group>"; };
		A100003E /* AutoSaveService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AutoSaveService.swift; sourceTree = "<group>"; };
		A100003F /* FileManagerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileManagerView.swift; sourceTree = "<group>"; };
		A1000040 /* AutoSaveSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AutoSaveSettingsView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A0FFFFFC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A0FFFFF6 = {
			isa = PBXGroup;
			children = (
				A1000001 /* object OCR */,
				A1000000 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1000000 /* Products */ = {
			isa = PBXGroup;
			children = (
				A0FFFFFF /* object OCR.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1000001 /* object OCR */ = {
			isa = PBXGroup;
			children = (
				A1000000 /* App.swift */,
				A1000002 /* ContentView.swift */,
				A100000E /* Models */,
				A100001E /* Services */,
				A100002E /* Views */,
				A1000004 /* Assets.xcassets */,
			);
			path = "object OCR";
			sourceTree = "<group>";
		};
		A100000E /* Models */ = {
			isa = PBXGroup;
			children = (
				A100000F /* HealthQuestion.swift */,
				A1000010 /* AIHealthModels.swift */,
				A1000011 /* ExtendedHealthModels.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A100001E /* Services */ = {
			isa = PBXGroup;
			children = (
				A100001F /* HealthQuestionService.swift */,
				A1000020 /* HealthAssessmentService.swift */,
				A1000021 /* UserProfileService.swift */,
				A1000022 /* HealthDataService.swift */,
				A1000023 /* HealthReportService.swift */,
				A1000024 /* HealthWisdomService.swift */,
				A1000025 /* AIDigitalDoctorService.swift */,
				A1000026 /* AdvancedHealthAnalysisService.swift */,
				A1000027 /* HealthTrendAnalysisService.swift */,
				A1000028 /* PersonalizedRecommendationEngine.swift */,
				A1000029 /* AIConversationService.swift */,
				A100002A /* MultiLanguageService.swift */,
				A100003E /* AutoSaveService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A100002E /* Views */ = {
			isa = PBXGroup;
			children = (
				A100002F /* HealthQuestionnaireView.swift */,
				A1000030 /* AssessmentView.swift */,
				A1000031 /* UserProfileView.swift */,
				A1000032 /* TrackingView.swift */,
				A1000033 /* AddHealthDataView.swift */,
				A1000034 /* ReportView.swift */,
				A1000035 /* HealthReportDetailView.swift */,
				A1000036 /* ProfileView.swift */,
				A1000037 /* WisdomDetailView.swift */,
				A1000038 /* AIDigitalDoctorMainView.swift */,
				A1000039 /* HealthAnalysisView.swift */,
				A100003A /* RealTimeMonitoringView.swift */,
				A100003B /* PersonalizedRecommendationsView.swift */,
				A100003C /* HealthResultsView.swift */,
				A100003D /* LanguageSelectorView.swift */,
				A100003F /* FileManagerView.swift */,
				A1000040 /* AutoSaveSettingsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A0FFFFFE /* object OCR */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A100000D /* Build configuration list for PBXNativeTarget "object OCR" */;
			buildPhases = (
				A0FFFFFB /* Sources */,
				A0FFFFFC /* Frameworks */,
				A0FFFFFD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "object OCR";
			productName = "object OCR";
			productReference = A0FFFFFF /* object OCR.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A0FFFFF7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A0FFFFFE = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A0FFFFFA /* Build configuration list for PBXProject "object OCR" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				"ug-CN",
			);
			mainGroup = A0FFFFF6;
			productRefGroup = A1000000 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A0FFFFFE /* object OCR */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A0FFFFFD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A1000005 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A0FFFFFB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A1000003 /* ContentView.swift in Sources */,
				A1000001 /* App.swift in Sources */,
				A1000010 /* HealthQuestion.swift in Sources */,
				A1000011 /* AIHealthModels.swift in Sources */,
				A1000012 /* ExtendedHealthModels.swift in Sources */,
				A1000020 /* HealthQuestionService.swift in Sources */,
				A1000021 /* HealthAssessmentService.swift in Sources */,
				A1000022 /* UserProfileService.swift in Sources */,
				A1000023 /* HealthDataService.swift in Sources */,
				A1000024 /* HealthReportService.swift in Sources */,
				A1000025 /* HealthWisdomService.swift in Sources */,
				A1000026 /* AIDigitalDoctorService.swift in Sources */,
				A1000027 /* AdvancedHealthAnalysisService.swift in Sources */,
				A1000028 /* HealthTrendAnalysisService.swift in Sources */,
				A1000029 /* PersonalizedRecommendationEngine.swift in Sources */,
				A100002A /* AIConversationService.swift in Sources */,
				A100002B /* MultiLanguageService.swift in Sources */,
				A1000030 /* HealthQuestionnaireView.swift in Sources */,
				A1000031 /* AssessmentView.swift in Sources */,
				A1000032 /* UserProfileView.swift in Sources */,
				A1000033 /* TrackingView.swift in Sources */,
				A1000034 /* AddHealthDataView.swift in Sources */,
				A1000035 /* ReportView.swift in Sources */,
				A1000036 /* HealthReportDetailView.swift in Sources */,
				A1000037 /* ProfileView.swift in Sources */,
				A1000038 /* WisdomDetailView.swift in Sources */,
				A1000039 /* AIDigitalDoctorMainView.swift in Sources */,
				A100003A /* HealthAnalysisView.swift in Sources */,
				A100003B /* RealTimeMonitoringView.swift in Sources */,
				A100003C /* PersonalizedRecommendationsView.swift in Sources */,
				A100003D /* HealthResultsView.swift in Sources */,
				A100003E /* LanguageSelectorView.swift in Sources */,
				A100003F /* AutoSaveService.swift in Sources */,
				A1000040 /* FileManagerView.swift in Sources */,
				A1000041 /* AutoSaveSettingsView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A100000B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A100000C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A100000E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "智能健康管理";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.healthai.object-OCR";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A100000F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "智能健康管理";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.healthai.object-OCR";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A0FFFFFA /* Build configuration list for PBXProject "object OCR" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A100000B /* Debug */,
				A100000C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A100000D /* Build configuration list for PBXNativeTarget "object OCR" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A100000E /* Debug */,
				A100000F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A0FFFFF7 /* Project object */;
}
