import Foundation
import SwiftUI

// 用户基本信息
struct UserProfile: ObservableObject {
    @Published var basicInfo = BasicInfo()
    @Published var healthMetrics = HealthMetrics()
    @Published var lifestyle = Lifestyle()
    @Published var medicalHistory = MedicalHistory()
    @Published var emotionalState = EmotionalState()
    @Published var dailyHabits = DailyHabits()
    
    // 计算BMI
    var bmi: Double {
        guard basicInfo.height > 0 && basicInfo.weight > 0 else { return 0 }
        let heightInMeters = basicInfo.height / 100
        return basicInfo.weight / (heightInMeters * heightInMeters)
    }
    
    // BMI分类
    var bmiCategory: BMICategory {
        switch bmi {
        case ..<18.5: return .underweight
        case 18.5..<25: return .normal
        case 25..<30: return .overweight
        default: return .obese
        }
    }
}

// 基本信息
struct BasicInfo {
    var name: String = ""
    var age: Int = 0
    var gender: Gender = .notSpecified
    var height: Double = 0 // cm
    var weight: Double = 0 // kg
    var phoneNumber: String = ""
    var email: String = ""
}

// 健康指标
struct HealthMetrics {
    var bloodSugar: Double = 0 // mg/dL
    var bloodPressure = BloodPressure()
    var heartRate: Int = 0
    var cholesterol = Cholesterol()
    var bodyFatPercentage: Double = 0
    var muscleMass: Double = 0
}

// 血压
struct BloodPressure {
    var systolic: Int = 0 // 收缩压
    var diastolic: Int = 0 // 舒张压
    
    var category: BloodPressureCategory {
        if systolic < 120 && diastolic < 80 {
            return .normal
        } else if systolic < 130 && diastolic < 80 {
            return .elevated
        } else if systolic < 140 && diastolic < 90 {
            return .stage1
        } else {
            return .stage2
        }
    }
}

// 胆固醇
struct Cholesterol {
    var total: Double = 0
    var hdl: Double = 0
    var ldl: Double = 0
    var triglycerides: Double = 0
}

// 生活方式
struct Lifestyle {
    var activityLevel: ActivityLevel = .sedentary
    var exerciseFrequency: ExerciseFrequency = .never
    var sleepHours: Double = 7.0
    var stressLevel: StressLevel = .low
    var smokingStatus: SmokingStatus = .never
    var alcoholConsumption: AlcoholConsumption = .never
}

// 病史
struct MedicalHistory {
    var chronicDiseases: [ChronicDisease] = []
    var allergies: [String] = []
    var medications: [String] = []
    var surgeries: [String] = []
    var familyHistory: [String] = []
}

// 情绪状态
struct EmotionalState {
    var mood: Mood = .neutral
    var anxietyLevel: AnxietyLevel = .low
    var depressionLevel: DepressionLevel = .low
    var stressTriggers: [String] = []
}

// 日常习惯
struct DailyHabits {
    var waterIntake: Double = 0 // liters
    var mealFrequency: Int = 3
    var screenTime: Double = 0 // hours
    var outdoorTime: Double = 0 // hours
    var socialInteraction: SocialInteraction = .moderate
}

// 枚举定义
enum Gender: String, CaseIterable {
    case male = "male"
    case female = "female"
    case other = "other"
    case notSpecified = "notSpecified"
}

enum BMICategory: String {
    case underweight = "underweight"
    case normal = "normal"
    case overweight = "overweight"
    case obese = "obese"
}

enum BloodPressureCategory: String {
    case normal = "normal"
    case elevated = "elevated"
    case stage1 = "stage1"
    case stage2 = "stage2"
}

enum ActivityLevel: String, CaseIterable {
    case sedentary = "sedentary"
    case lightlyActive = "lightlyActive"
    case moderatelyActive = "moderatelyActive"
    case veryActive = "veryActive"
    case extremelyActive = "extremelyActive"
}

enum ExerciseFrequency: String, CaseIterable {
    case never = "never"
    case rarely = "rarely"
    case sometimes = "sometimes"
    case often = "often"
    case daily = "daily"
}

enum StressLevel: String, CaseIterable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case veryHigh = "veryHigh"
}

enum SmokingStatus: String, CaseIterable {
    case never = "never"
    case former = "former"
    case current = "current"
}

enum AlcoholConsumption: String, CaseIterable {
    case never = "never"
    case rarely = "rarely"
    case moderate = "moderate"
    case heavy = "heavy"
}

enum Mood: String, CaseIterable {
    case veryHappy = "veryHappy"
    case happy = "happy"
    case neutral = "neutral"
    case sad = "sad"
    case verySad = "verySad"
}

enum AnxietyLevel: String, CaseIterable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case severe = "severe"
}

enum DepressionLevel: String, CaseIterable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case severe = "severe"
}

enum SocialInteraction: String, CaseIterable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
}

enum ChronicDisease: String, CaseIterable {
    case diabetes = "diabetes"
    case hypertension = "hypertension"
    case heartDisease = "heartDisease"
    case asthma = "asthma"
    case arthritis = "arthritis"
    case cancer = "cancer"
    case thyroid = "thyroid"
    case kidneyDisease = "kidneyDisease"
    case liverDisease = "liverDisease"
    case mentalHealth = "mentalHealth"
} 