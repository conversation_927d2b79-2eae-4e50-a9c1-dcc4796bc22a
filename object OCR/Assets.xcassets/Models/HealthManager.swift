import Foundation
import SwiftUI

class HealthManager: ObservableObject {
    @Published var dailyHealthQuote: String = ""
    @Published var recommendations: [HealthRecommendation] = []
    @Published var healthScore: Int = 0
    @Published var weeklyProgress: [HealthProgress] = []
    @Published var aiDoctorResponse: String = ""
    
    private let healthQuotes = [
        "ساغلاملىق - ئەڭ چوڭ بايلىق",
        "ساغلاملىق ئۈچۈن تەرتىپلىك ياشاش",
        "ساغلاملىق - ھاياتنىڭ نۇرى",
        "ساغلاملىق - كۈچلۈك ھايات",
        "ساغلاملىق - ئەڭ ياخشى دوست",
        "ساغلاملىق - ھاياتنىڭ مەركىزى",
        "ساغلاملىق - ئەڭ چوڭ نىمەت",
        "ساغلاملىق - ھاياتنىڭ ئاساسى",
        "ساغلاملىق - ئەڭ قىممەتلىك نەرسە",
        "ساغلاملىق - ھاياتنىڭ مەنىسى"
    ]
    
    init() {
        updateDailyQuote()
        calculateHealthScore()
    }
    
    // 更新每日健康语录
    func updateDailyQuote() {
        let calendar = Calendar.current
        let dayOfYear = calendar.ordinality(of: .day, in: .year, for: Date()) ?? 1
        let quoteIndex = (dayOfYear - 1) % healthQuotes.count
        dailyHealthQuote = healthQuotes[quoteIndex]
    }
    
    // 计算健康评分
    func calculateHealthScore() {
        // 这里将根据用户数据计算综合健康评分
        healthScore = 85 // 示例分数
    }
    
    // 生成个性化推荐
    func generateRecommendations(for userProfile: UserProfile) {
        recommendations.removeAll()
        
        // BMI相关推荐
        generateBMIRecommendations(for: userProfile)
        
        // 血压相关推荐
        generateBloodPressureRecommendations(for: userProfile)
        
        // 血糖相关推荐
        generateBloodSugarRecommendations(for: userProfile)
        
        // 生活方式推荐
        generateLifestyleRecommendations(for: userProfile)
        
        // 情绪健康推荐
        generateEmotionalHealthRecommendations(for: userProfile)
    }
    
    // BMI推荐
    private func generateBMIRecommendations(for userProfile: UserProfile) {
        let bmi = userProfile.bmi
        let category = userProfile.bmiCategory
        
        switch category {
        case .underweight:
            recommendations.append(HealthRecommendation(
                category: .nutrition,
                title: "体重过轻 - 营养建议",
                description: "建议增加热量摄入，多吃高蛋白食物如鸡蛋、瘦肉、豆类等",
                priority: .high,
                type: .diet
            ))
            recommendations.append(HealthRecommendation(
                category: .exercise,
                title: "增重运动建议",
                description: "进行力量训练，增加肌肉质量，避免过度有氧运动",
                priority: .medium,
                type: .exercise
            ))
            
        case .normal:
            recommendations.append(HealthRecommendation(
                category: .nutrition,
                title: "保持健康体重",
                description: "维持均衡饮食，多吃蔬菜水果，适量蛋白质",
                priority: .medium,
                type: .diet
            ))
            
        case .overweight:
            recommendations.append(HealthRecommendation(
                category: .nutrition,
                title: "减重饮食建议",
                description: "控制热量摄入，增加蔬菜水果，减少精制碳水化合物",
                priority: .high,
                type: .diet
            ))
            recommendations.append(HealthRecommendation(
                category: .exercise,
                title: "减重运动计划",
                description: "每周至少150分钟中等强度有氧运动，配合力量训练",
                priority: .high,
                type: .exercise
            ))
            
        case .obese:
            recommendations.append(HealthRecommendation(
                category: .nutrition,
                title: "肥胖管理 - 饮食调整",
                description: "严格控制热量摄入，增加蛋白质比例，减少脂肪和糖分",
                priority: .critical,
                type: .diet
            ))
            recommendations.append(HealthRecommendation(
                category: .exercise,
                title: "肥胖管理 - 运动计划",
                description: "从低强度运动开始，逐步增加运动量，建议咨询医生",
                priority: .critical,
                type: .exercise
            ))
        }
    }
    
    // 血压推荐
    private func generateBloodPressureRecommendations(for userProfile: UserProfile) {
        let bp = userProfile.healthMetrics.bloodPressure
        
        switch bp.category {
        case .normal:
            recommendations.append(HealthRecommendation(
                category: .health,
                title: "血压正常",
                description: "继续保持健康的生活方式，定期监测血压",
                priority: .low,
                type: .monitoring
            ))
            
        case .elevated:
            recommendations.append(HealthRecommendation(
                category: .health,
                title: "血压偏高",
                description: "减少盐分摄入，增加运动，控制体重，定期监测",
                priority: .medium,
                type: .lifestyle
            ))
            
        case .stage1:
            recommendations.append(HealthRecommendation(
                category: .health,
                title: "高血压一级",
                description: "立即调整生活方式，减少盐分，增加运动，考虑药物治疗",
                priority: .high,
                type: .medical
            ))
            
        case .stage2:
            recommendations.append(HealthRecommendation(
                category: .health,
                title: "高血压二级",
                description: "需要立即就医，药物治疗配合生活方式调整",
                priority: .critical,
                type: .medical
            ))
        }
    }
    
    // 血糖推荐
    private func generateBloodSugarRecommendations(for userProfile: UserProfile) {
        let bloodSugar = userProfile.healthMetrics.bloodSugar
        
        if bloodSugar > 126 {
            recommendations.append(HealthRecommendation(
                category: .health,
                title: "血糖偏高",
                description: "控制碳水化合物摄入，增加运动，定期监测血糖",
                priority: .high,
                type: .medical
            ))
        } else if bloodSugar > 100 {
            recommendations.append(HealthRecommendation(
                category: .health,
                title: "血糖临界",
                description: "注意饮食控制，增加运动，预防糖尿病",
                priority: .medium,
                type: .lifestyle
            ))
        }
    }
    
    // 生活方式推荐
    private func generateLifestyleRecommendations(for userProfile: UserProfile) {
        let lifestyle = userProfile.lifestyle
        
        // 运动频率推荐
        if lifestyle.exerciseFrequency == .never || lifestyle.exerciseFrequency == .rarely {
            recommendations.append(HealthRecommendation(
                category: .exercise,
                title: "增加运动量",
                description: "建议每周至少进行150分钟中等强度运动",
                priority: .high,
                type: .exercise
            ))
        }
        
        // 睡眠推荐
        if lifestyle.sleepHours < 7 {
            recommendations.append(HealthRecommendation(
                category: .lifestyle,
                title: "改善睡眠",
                description: "建议每晚睡眠7-9小时，保持规律作息",
                priority: .medium,
                type: .lifestyle
            ))
        }
        
        // 压力管理
        if lifestyle.stressLevel == .high || lifestyle.stressLevel == .veryHigh {
            recommendations.append(HealthRecommendation(
                category: .mental,
                title: "压力管理",
                description: "尝试冥想、深呼吸、瑜伽等放松技巧",
                priority: .high,
                type: .lifestyle
            ))
        }
    }
    
    // 情绪健康推荐
    private func generateEmotionalHealthRecommendations(for userProfile: UserProfile) {
        let emotional = userProfile.emotionalState
        
        if emotional.anxietyLevel == .high || emotional.anxietyLevel == .severe {
            recommendations.append(HealthRecommendation(
                category: .mental,
                title: "焦虑管理",
                description: "建议寻求专业心理咨询，学习放松技巧",
                priority: .high,
                type: .medical
            ))
        }
        
        if emotional.depressionLevel == .high || emotional.depressionLevel == .severe {
            recommendations.append(HealthRecommendation(
                category: .mental,
                title: "抑郁症状",
                description: "建议立即寻求专业心理健康服务",
                priority: .critical,
                type: .medical
            ))
        }
    }
    
    // AI医生咨询
    func consultAIDoctor(userQuestion: String, userProfile: UserProfile) {
        // 模拟AI医生回复
        let responses = [
            "根据您的健康数据，我建议您注意以下几点...",
            "您的BMI显示需要调整饮食结构，建议...",
            "考虑到您的血压情况，我推荐...",
            "基于您的运动习惯，我建议增加...",
            "您的睡眠质量需要改善，建议..."
        ]
        
        aiDoctorResponse = responses.randomElement() ?? "请提供更多详细信息以便我给出更准确的建议。"
    }
}

// 健康推荐模型
struct HealthRecommendation: Identifiable {
    let id = UUID()
    let category: RecommendationCategory
    let title: String
    let description: String
    let priority: RecommendationPriority
    let type: RecommendationType
}

// 健康进度
struct HealthProgress: Identifiable {
    let id = UUID()
    let date: Date
    let score: Int
    let category: String
}

// 推荐分类
enum RecommendationCategory: String, CaseIterable {
    case nutrition = "nutrition"
    case exercise = "exercise"
    case health = "health"
    case lifestyle = "lifestyle"
    case mental = "mental"
}

// 推荐优先级
enum RecommendationPriority: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
}

// 推荐类型
enum RecommendationType: String, CaseIterable {
    case diet = "diet"
    case exercise = "exercise"
    case lifestyle = "lifestyle"
    case medical = "medical"
    case monitoring = "monitoring"
} 