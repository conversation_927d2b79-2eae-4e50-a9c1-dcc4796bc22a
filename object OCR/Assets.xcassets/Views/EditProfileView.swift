import SwiftUI

struct EditProfileView: View {
    @EnvironmentObject var userProfile: UserProfile
    @Environment(\.dismiss) private var dismiss
    @State private var tempProfile = UserProfile()
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 基本信息编辑
                    BasicInfoEditSection()
                    
                    // 健康指标编辑
                    HealthMetricsEditSection()
                    
                    // 生活方式编辑
                    LifestyleEditSection()
                    
                    // 病史编辑
                    MedicalHistoryEditSection()
                    
                    // 情绪状态编辑
                    EmotionalStateEditSection()
                    
                    // 日常习惯编辑
                    DailyHabitsEditSection()
                }
                .padding()
            }
            .navigationTitle("编辑档案")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveProfile()
                        dismiss()
                    }
                }
            }
            .onAppear {
                tempProfile = userProfile
            }
        }
    }
    
    private func saveProfile() {
        userProfile.basicInfo = tempProfile.basicInfo
        userProfile.healthMetrics = tempProfile.healthMetrics
        userProfile.lifestyle = tempProfile.lifestyle
        userProfile.medicalHistory = tempProfile.medicalHistory
        userProfile.emotionalState = tempProfile.emotionalState
        userProfile.dailyHabits = tempProfile.dailyHabits
    }
}

// 基本信息编辑部分
struct BasicInfoEditSection: View {
    @EnvironmentObject var tempProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "person.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("基本信息")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                TextField("姓名", text: $tempProfile.basicInfo.name)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                HStack {
                    Text("年龄")
                    Spacer()
                    TextField("年龄", value: $tempProfile.basicInfo.age, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("性别")
                    Spacer()
                    Picker("性别", selection: $tempProfile.basicInfo.gender) {
                        ForEach(Gender.allCases, id: \.self) { gender in
                            Text(getGenderText(gender)).tag(gender)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("身高 (cm)")
                    Spacer()
                    TextField("身高", value: $tempProfile.basicInfo.height, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("体重 (kg)")
                    Spacer()
                    TextField("体重", value: $tempProfile.basicInfo.weight, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getGenderText(_ gender: Gender) -> String {
        switch gender {
        case .male: return "男"
        case .female: return "女"
        case .other: return "其他"
        case .notSpecified: return "未设置"
        }
    }
}

// 健康指标编辑部分
struct HealthMetricsEditSection: View {
    @EnvironmentObject var tempProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.red)
                    .font(.title2)
                
                Text("健康指标")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                HStack {
                    Text("血糖 (mg/dL)")
                    Spacer()
                    TextField("血糖", value: $tempProfile.healthMetrics.bloodSugar, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("收缩压")
                    Spacer()
                    TextField("收缩压", value: $tempProfile.healthMetrics.bloodPressure.systolic, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 80)
                    
                    Text("/")
                    
                    TextField("舒张压", value: $tempProfile.healthMetrics.bloodPressure.diastolic, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 80)
                    
                    Text("mmHg")
                }
                
                HStack {
                    Text("心率 (bpm)")
                    Spacer()
                    TextField("心率", value: $tempProfile.healthMetrics.heartRate, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("体脂率 (%)")
                    Spacer()
                    TextField("体脂率", value: $tempProfile.healthMetrics.bodyFatPercentage, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("肌肉量 (kg)")
                    Spacer()
                    TextField("肌肉量", value: $tempProfile.healthMetrics.muscleMass, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// 生活方式编辑部分
struct LifestyleEditSection: View {
    @EnvironmentObject var tempProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "figure.walk")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("生活方式")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                HStack {
                    Text("活动水平")
                    Spacer()
                    Picker("活动水平", selection: $tempProfile.lifestyle.activityLevel) {
                        ForEach(ActivityLevel.allCases, id: \.self) { level in
                            Text(getActivityLevelText(level)).tag(level)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("运动频率")
                    Spacer()
                    Picker("运动频率", selection: $tempProfile.lifestyle.exerciseFrequency) {
                        ForEach(ExerciseFrequency.allCases, id: \.self) { frequency in
                            Text(getExerciseFrequencyText(frequency)).tag(frequency)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("睡眠时间 (小时)")
                    Spacer()
                    TextField("睡眠时间", value: $tempProfile.lifestyle.sleepHours, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("压力水平")
                    Spacer()
                    Picker("压力水平", selection: $tempProfile.lifestyle.stressLevel) {
                        ForEach(StressLevel.allCases, id: \.self) { level in
                            Text(getStressLevelText(level)).tag(level)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("吸烟状态")
                    Spacer()
                    Picker("吸烟状态", selection: $tempProfile.lifestyle.smokingStatus) {
                        ForEach(SmokingStatus.allCases, id: \.self) { status in
                            Text(getSmokingStatusText(status)).tag(status)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("饮酒情况")
                    Spacer()
                    Picker("饮酒情况", selection: $tempProfile.lifestyle.alcoholConsumption) {
                        ForEach(AlcoholConsumption.allCases, id: \.self) { consumption in
                            Text(getAlcoholConsumptionText(consumption)).tag(consumption)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getActivityLevelText(_ level: ActivityLevel) -> String {
        switch level {
        case .sedentary: return "久坐不动"
        case .lightlyActive: return "轻度活动"
        case .moderatelyActive: return "中度活动"
        case .veryActive: return "高度活动"
        case .extremelyActive: return "极高活动"
        }
    }
    
    private func getExerciseFrequencyText(_ frequency: ExerciseFrequency) -> String {
        switch frequency {
        case .never: return "从不运动"
        case .rarely: return "很少运动"
        case .sometimes: return "偶尔运动"
        case .often: return "经常运动"
        case .daily: return "每天运动"
        }
    }
    
    private func getStressLevelText(_ level: StressLevel) -> String {
        switch level {
        case .low: return "低压力"
        case .moderate: return "中等压力"
        case .high: return "高压力"
        case .veryHigh: return "极高压力"
        }
    }
    
    private func getSmokingStatusText(_ status: SmokingStatus) -> String {
        switch status {
        case .never: return "从不吸烟"
        case .former: return "已戒烟"
        case .current: return "目前吸烟"
        }
    }
    
    private func getAlcoholConsumptionText(_ consumption: AlcoholConsumption) -> String {
        switch consumption {
        case .never: return "从不饮酒"
        case .rarely: return "很少饮酒"
        case .moderate: return "适量饮酒"
        case .heavy: return "大量饮酒"
        }
    }
}

// 病史编辑部分
struct MedicalHistoryEditSection: View {
    @EnvironmentObject var tempProfile: UserProfile
    @State private var showingDiseasePicker = false
    @State private var newAllergy = ""
    @State private var newMedication = ""
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "cross.fill")
                    .foregroundColor(.orange)
                    .font(.title2)
                
                Text("病史")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                // 慢性疾病
                VStack(alignment: .leading, spacing: 8) {
                    Text("慢性疾病")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    if tempProfile.medicalHistory.chronicDiseases.isEmpty {
                        Text("无慢性疾病")
                            .foregroundColor(.secondary)
                    } else {
                        ForEach(tempProfile.medicalHistory.chronicDiseases, id: \.self) { disease in
                            HStack {
                                Text(getChronicDiseaseText(disease))
                                Spacer()
                                Button("删除") {
                                    tempProfile.medicalHistory.chronicDiseases.removeAll { $0 == disease }
                                }
                                .foregroundColor(.red)
                            }
                        }
                    }
                    
                    Button("添加慢性疾病") {
                        showingDiseasePicker = true
                    }
                    .foregroundColor(.blue)
                }
                
                Divider()
                
                // 过敏史
                VStack(alignment: .leading, spacing: 8) {
                    Text("过敏史")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    HStack {
                        TextField("添加过敏原", text: $newAllergy)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        
                        Button("添加") {
                            if !newAllergy.isEmpty {
                                tempProfile.medicalHistory.allergies.append(newAllergy)
                                newAllergy = ""
                            }
                        }
                        .disabled(newAllergy.isEmpty)
                    }
                    
                    ForEach(tempProfile.medicalHistory.allergies, id: \.self) { allergy in
                        HStack {
                            Text(allergy)
                            Spacer()
                            Button("删除") {
                                tempProfile.medicalHistory.allergies.removeAll { $0 == allergy }
                            }
                            .foregroundColor(.red)
                        }
                    }
                }
                
                Divider()
                
                // 用药情况
                VStack(alignment: .leading, spacing: 8) {
                    Text("用药情况")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    HStack {
                        TextField("添加药物", text: $newMedication)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        
                        Button("添加") {
                            if !newMedication.isEmpty {
                                tempProfile.medicalHistory.medications.append(newMedication)
                                newMedication = ""
                            }
                        }
                        .disabled(newMedication.isEmpty)
                    }
                    
                    ForEach(tempProfile.medicalHistory.medications, id: \.self) { medication in
                        HStack {
                            Text(medication)
                            Spacer()
                            Button("删除") {
                                tempProfile.medicalHistory.medications.removeAll { $0 == medication }
                            }
                            .foregroundColor(.red)
                        }
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .sheet(isPresented: $showingDiseasePicker) {
            DiseasePickerView(selectedDiseases: $tempProfile.medicalHistory.chronicDiseases)
        }
    }
    
    private func getChronicDiseaseText(_ disease: ChronicDisease) -> String {
        switch disease {
        case .diabetes: return "糖尿病"
        case .hypertension: return "高血压"
        case .heartDisease: return "心脏病"
        case .asthma: return "哮喘"
        case .arthritis: return "关节炎"
        case .cancer: return "癌症"
        case .thyroid: return "甲状腺疾病"
        case .kidneyDisease: return "肾病"
        case .liverDisease: return "肝病"
        case .mentalHealth: return "心理健康问题"
        }
    }
}

// 疾病选择器
struct DiseasePickerView: View {
    @Binding var selectedDiseases: [ChronicDisease]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(ChronicDisease.allCases, id: \.self) { disease in
                    HStack {
                        Text(getChronicDiseaseText(disease))
                        Spacer()
                        if selectedDiseases.contains(disease) {
                            Image(systemName: "checkmark")
                                .foregroundColor(.blue)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if selectedDiseases.contains(disease) {
                            selectedDiseases.removeAll { $0 == disease }
                        } else {
                            selectedDiseases.append(disease)
                        }
                    }
                }
            }
            .navigationTitle("选择慢性疾病")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func getChronicDiseaseText(_ disease: ChronicDisease) -> String {
        switch disease {
        case .diabetes: return "糖尿病"
        case .hypertension: return "高血压"
        case .heartDisease: return "心脏病"
        case .asthma: return "哮喘"
        case .arthritis: return "关节炎"
        case .cancer: return "癌症"
        case .thyroid: return "甲状腺疾病"
        case .kidneyDisease: return "肾病"
        case .liverDisease: return "肝病"
        case .mentalHealth: return "心理健康问题"
        }
    }
}

// 情绪状态编辑部分
struct EmotionalStateEditSection: View {
    @EnvironmentObject var tempProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.purple)
                    .font(.title2)
                
                Text("情绪状态")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                HStack {
                    Text("心情")
                    Spacer()
                    Picker("心情", selection: $tempProfile.emotionalState.mood) {
                        ForEach(Mood.allCases, id: \.self) { mood in
                            Text(getMoodText(mood)).tag(mood)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("焦虑水平")
                    Spacer()
                    Picker("焦虑水平", selection: $tempProfile.emotionalState.anxietyLevel) {
                        ForEach(AnxietyLevel.allCases, id: \.self) { level in
                            Text(getAnxietyLevelText(level)).tag(level)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("抑郁水平")
                    Spacer()
                    Picker("抑郁水平", selection: $tempProfile.emotionalState.depressionLevel) {
                        ForEach(DepressionLevel.allCases, id: \.self) { level in
                            Text(getDepressionLevelText(level)).tag(level)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getMoodText(_ mood: Mood) -> String {
        switch mood {
        case .veryHappy: return "非常开心"
        case .happy: return "开心"
        case .neutral: return "一般"
        case .sad: return "难过"
        case .verySad: return "非常难过"
        }
    }
    
    private func getAnxietyLevelText(_ level: AnxietyLevel) -> String {
        switch level {
        case .low: return "低焦虑"
        case .moderate: return "中等焦虑"
        case .high: return "高焦虑"
        case .severe: return "严重焦虑"
        }
    }
    
    private func getDepressionLevelText(_ level: DepressionLevel) -> String {
        switch level {
        case .low: return "低抑郁"
        case .moderate: return "中等抑郁"
        case .high: return "高抑郁"
        case .severe: return "严重抑郁"
        }
    }
}

// 日常习惯编辑部分
struct DailyHabitsEditSection: View {
    @EnvironmentObject var tempProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "clock.fill")
                    .foregroundColor(.cyan)
                    .font(.title2)
                
                Text("日常习惯")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                HStack {
                    Text("每日饮水量 (L)")
                    Spacer()
                    TextField("饮水量", value: $tempProfile.dailyHabits.waterIntake, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("每日餐数")
                    Spacer()
                    TextField("餐数", value: $tempProfile.dailyHabits.mealFrequency, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("屏幕时间 (小时)")
                    Spacer()
                    TextField("屏幕时间", value: $tempProfile.dailyHabits.screenTime, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("户外时间 (小时)")
                    Spacer()
                    TextField("户外时间", value: $tempProfile.dailyHabits.outdoorTime, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 100)
                }
                
                HStack {
                    Text("社交互动")
                    Spacer()
                    Picker("社交互动", selection: $tempProfile.dailyHabits.socialInteraction) {
                        ForEach(SocialInteraction.allCases, id: \.self) { interaction in
                            Text(getSocialInteractionText(interaction)).tag(interaction)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getSocialInteractionText(_ interaction: SocialInteraction) -> String {
        switch interaction {
        case .low: return "较少"
        case .moderate: return "中等"
        case .high: return "较多"
        }
    }
}

#Preview {
    EditProfileView()
        .environmentObject(UserProfile())
} 