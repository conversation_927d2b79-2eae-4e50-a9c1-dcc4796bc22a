import SwiftUI

struct ContentView: View {
    @EnvironmentObject var healthManager: HealthManager
    @EnvironmentObject var userProfile: UserProfile
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 主页
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("主页")
                }
                .tag(0)
            
            // 健康档案
            HealthProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("健康档案")
                }
                .tag(1)
            
            // 推荐
            RecommendationsView()
                .tabItem {
                    Image(systemName: "heart.fill")
                    Text("健康推荐")
                }
                .tag(2)
            
            // AI医生
            AIDoctorView()
                .tabItem {
                    Image(systemName: "brain.head.profile")
                    Text("AI医生")
                }
                .tag(3)
            
            // 进度跟踪
            ProgressTrackingView()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("进度跟踪")
                }
                .tag(4)
        }
        .accentColor(.green)
        .onAppear {
            healthManager.generateRecommendations(for: userProfile)
        }
    }
}

// 主页视图
struct HomeView: View {
    @EnvironmentObject var healthManager: HealthManager
    @EnvironmentObject var userProfile: UserProfile
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 每日健康语录
                    DailyQuoteCard()
                    
                    // 健康评分卡片
                    HealthScoreCard()
                    
                    // 快速操作
                    QuickActionsView()
                    
                    // 今日建议
                    TodayRecommendationsView()
                }
                .padding()
            }
            .navigationTitle("健康AI助手")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

// 每日健康语录卡片
struct DailyQuoteCard: View {
    @EnvironmentObject var healthManager: HealthManager
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "quote.bubble.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("今日健康语录")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            Text(healthManager.dailyHealthQuote)
                .font(.title3)
                .fontWeight(.medium)
                .multilineTextAlignment(.center)
                .padding(.vertical, 8)
            
            Text("Health is the greatest wealth")
                .font(.caption)
                .foregroundColor(.secondary)
                .italic()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// 健康评分卡片
struct HealthScoreCard: View {
    @EnvironmentObject var healthManager: HealthManager
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.red)
                    .font(.title2)
                
                Text("健康评分")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            HStack {
                VStack {
                    Text("\(healthManager.healthScore)")
                        .font(.system(size: 48, weight: .bold))
                        .foregroundColor(.green)
                    
                    Text("总分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text(getHealthStatusText())
                        .font(.headline)
                        .foregroundColor(getHealthStatusColor())
                    
                    Text(getHealthAdvice())
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.trailing)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getHealthStatusText() -> String {
        switch healthManager.healthScore {
        case 90...100: return "优秀"
        case 80..<90: return "良好"
        case 70..<80: return "一般"
        case 60..<70: return "需要改善"
        default: return "需要关注"
        }
    }
    
    private func getHealthStatusColor() -> Color {
        switch healthManager.healthScore {
        case 90...100: return .green
        case 80..<90: return .blue
        case 70..<80: return .orange
        case 60..<70: return .red
        default: return .red
        }
    }
    
    private func getHealthAdvice() -> String {
        switch healthManager.healthScore {
        case 90...100: return "继续保持健康的生活方式"
        case 80..<90: return "继续保持，可以进一步优化"
        case 70..<80: return "需要调整一些生活习惯"
        case 60..<70: return "建议咨询医生或营养师"
        default: return "建议立即就医检查"
        }
    }
}

// 快速操作视图
struct QuickActionsView: View {
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "bolt.fill")
                    .foregroundColor(.yellow)
                    .font(.title2)
                
                Text("快速操作")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                QuickActionButton(title: "记录体重", icon: "scalemass.fill", color: .blue)
                QuickActionButton(title: "记录血压", icon: "heart.fill", color: .red)
                QuickActionButton(title: "记录血糖", icon: "drop.fill", color: .purple)
                QuickActionButton(title: "记录运动", icon: "figure.walk", color: .green)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// 快速操作按钮
struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    
    var body: some View {
        Button(action: {
            // 处理快速操作
        }) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 今日建议视图
struct TodayRecommendationsView: View {
    @EnvironmentObject var healthManager: HealthManager
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                    .font(.title2)
                
                Text("今日建议")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            if healthManager.recommendations.isEmpty {
                Text("暂无建议")
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ForEach(healthManager.recommendations.prefix(3)) { recommendation in
                    RecommendationRow(recommendation: recommendation)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// 建议行
struct RecommendationRow: View {
    let recommendation: HealthRecommendation
    
    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(getPriorityColor())
                .frame(width: 8, height: 8)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(recommendation.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
    
    private func getPriorityColor() -> Color {
        switch recommendation.priority {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(HealthManager())
        .environmentObject(UserProfile())
} 