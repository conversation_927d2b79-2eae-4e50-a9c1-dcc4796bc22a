import SwiftUI

struct HealthProfileView: View {
    @EnvironmentObject var userProfile: UserProfile
    @State private var showingEditProfile = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 基本信息卡片
                    BasicInfoCard()
                    
                    // 健康指标卡片
                    HealthMetricsCard()
                    
                    // 生活方式卡片
                    LifestyleCard()
                    
                    // 病史卡片
                    MedicalHistoryCard()
                    
                    // 情绪状态卡片
                    EmotionalStateCard()
                    
                    // 日常习惯卡片
                    DailyHabitsCard()
                }
                .padding()
            }
            .navigationTitle("健康档案")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("编辑") {
                        showingEditProfile = true
                    }
                }
            }
            .sheet(isPresented: $showingEditProfile) {
                EditProfileView()
            }
        }
    }
}

// 基本信息卡片
struct BasicInfoCard: View {
    @EnvironmentObject var userProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "person.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("基本信息")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                InfoRow(title: "姓名", value: userProfile.basicInfo.name.isEmpty ? "未设置" : userProfile.basicInfo.name)
                InfoRow(title: "年龄", value: userProfile.basicInfo.age == 0 ? "未设置" : "\(userProfile.basicInfo.age)岁")
                InfoRow(title: "性别", value: getGenderText(userProfile.basicInfo.gender))
                InfoRow(title: "身高", value: userProfile.basicInfo.height == 0 ? "未设置" : "\(Int(userProfile.basicInfo.height))cm")
                InfoRow(title: "体重", value: userProfile.basicInfo.weight == 0 ? "未设置" : "\(Int(userProfile.basicInfo.weight))kg")
                
                if userProfile.bmi > 0 {
                    InfoRow(title: "BMI", value: String(format: "%.1f", userProfile.bmi))
                    InfoRow(title: "BMI分类", value: getBMICategoryText(userProfile.bmiCategory))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getGenderText(_ gender: Gender) -> String {
        switch gender {
        case .male: return "男"
        case .female: return "女"
        case .other: return "其他"
        case .notSpecified: return "未设置"
        }
    }
    
    private func getBMICategoryText(_ category: BMICategory) -> String {
        switch category {
        case .underweight: return "体重过轻"
        case .normal: return "正常"
        case .overweight: return "超重"
        case .obese: return "肥胖"
        }
    }
}

// 健康指标卡片
struct HealthMetricsCard: View {
    @EnvironmentObject var userProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.red)
                    .font(.title2)
                
                Text("健康指标")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                InfoRow(title: "血糖", value: userProfile.healthMetrics.bloodSugar == 0 ? "未设置" : "\(Int(userProfile.healthMetrics.bloodSugar)) mg/dL")
                InfoRow(title: "血压", value: getBloodPressureText())
                InfoRow(title: "心率", value: userProfile.healthMetrics.heartRate == 0 ? "未设置" : "\(userProfile.healthMetrics.heartRate) bpm")
                InfoRow(title: "体脂率", value: userProfile.healthMetrics.bodyFatPercentage == 0 ? "未设置" : "\(Int(userProfile.healthMetrics.bodyFatPercentage))%")
                InfoRow(title: "肌肉量", value: userProfile.healthMetrics.muscleMass == 0 ? "未设置" : "\(Int(userProfile.healthMetrics.muscleMass))kg")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getBloodPressureText() -> String {
        let bp = userProfile.healthMetrics.bloodPressure
        if bp.systolic == 0 && bp.diastolic == 0 {
            return "未设置"
        }
        return "\(bp.systolic)/\(bp.diastolic) mmHg"
    }
}

// 生活方式卡片
struct LifestyleCard: View {
    @EnvironmentObject var userProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "figure.walk")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("生活方式")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                InfoRow(title: "活动水平", value: getActivityLevelText(userProfile.lifestyle.activityLevel))
                InfoRow(title: "运动频率", value: getExerciseFrequencyText(userProfile.lifestyle.exerciseFrequency))
                InfoRow(title: "睡眠时间", value: "\(userProfile.lifestyle.sleepHours)小时")
                InfoRow(title: "压力水平", value: getStressLevelText(userProfile.lifestyle.stressLevel))
                InfoRow(title: "吸烟状态", value: getSmokingStatusText(userProfile.lifestyle.smokingStatus))
                InfoRow(title: "饮酒情况", value: getAlcoholConsumptionText(userProfile.lifestyle.alcoholConsumption))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getActivityLevelText(_ level: ActivityLevel) -> String {
        switch level {
        case .sedentary: return "久坐不动"
        case .lightlyActive: return "轻度活动"
        case .moderatelyActive: return "中度活动"
        case .veryActive: return "高度活动"
        case .extremelyActive: return "极高活动"
        }
    }
    
    private func getExerciseFrequencyText(_ frequency: ExerciseFrequency) -> String {
        switch frequency {
        case .never: return "从不运动"
        case .rarely: return "很少运动"
        case .sometimes: return "偶尔运动"
        case .often: return "经常运动"
        case .daily: return "每天运动"
        }
    }
    
    private func getStressLevelText(_ level: StressLevel) -> String {
        switch level {
        case .low: return "低压力"
        case .moderate: return "中等压力"
        case .high: return "高压力"
        case .veryHigh: return "极高压力"
        }
    }
    
    private func getSmokingStatusText(_ status: SmokingStatus) -> String {
        switch status {
        case .never: return "从不吸烟"
        case .former: return "已戒烟"
        case .current: return "目前吸烟"
        }
    }
    
    private func getAlcoholConsumptionText(_ consumption: AlcoholConsumption) -> String {
        switch consumption {
        case .never: return "从不饮酒"
        case .rarely: return "很少饮酒"
        case .moderate: return "适量饮酒"
        case .heavy: return "大量饮酒"
        }
    }
}

// 病史卡片
struct MedicalHistoryCard: View {
    @EnvironmentObject var userProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "cross.fill")
                    .foregroundColor(.orange)
                    .font(.title2)
                
                Text("病史")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                if userProfile.medicalHistory.chronicDiseases.isEmpty {
                    InfoRow(title: "慢性疾病", value: "无")
                } else {
                    ForEach(userProfile.medicalHistory.chronicDiseases, id: \.self) { disease in
                        InfoRow(title: "慢性疾病", value: getChronicDiseaseText(disease))
                    }
                }
                
                if userProfile.medicalHistory.allergies.isEmpty {
                    InfoRow(title: "过敏史", value: "无")
                } else {
                    ForEach(userProfile.medicalHistory.allergies, id: \.self) { allergy in
                        InfoRow(title: "过敏史", value: allergy)
                    }
                }
                
                if userProfile.medicalHistory.medications.isEmpty {
                    InfoRow(title: "用药情况", value: "无")
                } else {
                    ForEach(userProfile.medicalHistory.medications, id: \.self) { medication in
                        InfoRow(title: "用药情况", value: medication)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getChronicDiseaseText(_ disease: ChronicDisease) -> String {
        switch disease {
        case .diabetes: return "糖尿病"
        case .hypertension: return "高血压"
        case .heartDisease: return "心脏病"
        case .asthma: return "哮喘"
        case .arthritis: return "关节炎"
        case .cancer: return "癌症"
        case .thyroid: return "甲状腺疾病"
        case .kidneyDisease: return "肾病"
        case .liverDisease: return "肝病"
        case .mentalHealth: return "心理健康问题"
        }
    }
}

// 情绪状态卡片
struct EmotionalStateCard: View {
    @EnvironmentObject var userProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.purple)
                    .font(.title2)
                
                Text("情绪状态")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                InfoRow(title: "心情", value: getMoodText(userProfile.emotionalState.mood))
                InfoRow(title: "焦虑水平", value: getAnxietyLevelText(userProfile.emotionalState.anxietyLevel))
                InfoRow(title: "抑郁水平", value: getDepressionLevelText(userProfile.emotionalState.depressionLevel))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getMoodText(_ mood: Mood) -> String {
        switch mood {
        case .veryHappy: return "非常开心"
        case .happy: return "开心"
        case .neutral: return "一般"
        case .sad: return "难过"
        case .verySad: return "非常难过"
        }
    }
    
    private func getAnxietyLevelText(_ level: AnxietyLevel) -> String {
        switch level {
        case .low: return "低焦虑"
        case .moderate: return "中等焦虑"
        case .high: return "高焦虑"
        case .severe: return "严重焦虑"
        }
    }
    
    private func getDepressionLevelText(_ level: DepressionLevel) -> String {
        switch level {
        case .low: return "低抑郁"
        case .moderate: return "中等抑郁"
        case .high: return "高抑郁"
        case .severe: return "严重抑郁"
        }
    }
}

// 日常习惯卡片
struct DailyHabitsCard: View {
    @EnvironmentObject var userProfile: UserProfile
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "clock.fill")
                    .foregroundColor(.cyan)
                    .font(.title2)
                
                Text("日常习惯")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                InfoRow(title: "每日饮水量", value: "\(userProfile.dailyHabits.waterIntake)L")
                InfoRow(title: "每日餐数", value: "\(userProfile.dailyHabits.mealFrequency)餐")
                InfoRow(title: "屏幕时间", value: "\(userProfile.dailyHabits.screenTime)小时")
                InfoRow(title: "户外时间", value: "\(userProfile.dailyHabits.outdoorTime)小时")
                InfoRow(title: "社交互动", value: getSocialInteractionText(userProfile.dailyHabits.socialInteraction))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getSocialInteractionText(_ interaction: SocialInteraction) -> String {
        switch interaction {
        case .low: return "较少"
        case .moderate: return "中等"
        case .high: return "较多"
        }
    }
}

// 信息行
struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

#Preview {
    HealthProfileView()
        .environmentObject(UserProfile())
} 