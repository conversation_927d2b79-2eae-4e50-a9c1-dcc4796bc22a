import SwiftUI

struct RecommendationsView: View {
    @EnvironmentObject var healthManager: HealthManager
    @EnvironmentObject var userProfile: UserProfile
    @State private var selectedCategory: RecommendationCategory? = nil
    
    var filteredRecommendations: [HealthRecommendation] {
        if let category = selectedCategory {
            return healthManager.recommendations.filter { $0.category == category }
        }
        return healthManager.recommendations
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 分类筛选器
                CategoryFilterView(selectedCategory: $selectedCategory)
                
                // 推荐列表
                ScrollView {
                    LazyVStack(spacing: 16) {
                        ForEach(filteredRecommendations) { recommendation in
                            RecommendationCard(recommendation: recommendation)
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("健康推荐")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                healthManager.generateRecommendations(for: userProfile)
            }
        }
    }
}

// 分类筛选器
struct CategoryFilterView: View {
    @Binding var selectedCategory: RecommendationCategory?
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                CategoryButton(title: "全部", isSelected: selectedCategory == nil) {
                    selectedCategory = nil
                }
                
                ForEach(RecommendationCategory.allCases, id: \.self) { category in
                    CategoryButton(
                        title: getCategoryText(category),
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    private func getCategoryText(_ category: RecommendationCategory) -> String {
        switch category {
        case .nutrition: return "营养"
        case .exercise: return "运动"
        case .health: return "健康"
        case .lifestyle: return "生活方式"
        case .mental: return "心理健康"
        }
    }
}

// 分类按钮
struct CategoryButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.green : Color(.systemGray5))
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 推荐卡片
struct RecommendationCard: View {
    let recommendation: HealthRecommendation
    @State private var isExpanded = false
    
    var body: some View {
        VStack(spacing: 12) {
            // 头部
            HStack {
                Image(systemName: getCategoryIcon())
                    .foregroundColor(getCategoryColor())
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(recommendation.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(getCategoryText())
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                PriorityBadge(priority: recommendation.priority)
            }
            
            // 描述
            Text(recommendation.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(isExpanded ? nil : 3)
            
            // 展开按钮
            if recommendation.description.count > 100 {
                Button(isExpanded ? "收起" : "展开") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            // 操作按钮
            HStack {
                Button("了解更多") {
                    // 处理了解更多
                }
                .font(.caption)
                .foregroundColor(.blue)
                
                Spacer()
                
                Button("标记完成") {
                    // 处理标记完成
                }
                .font(.caption)
                .foregroundColor(.green)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getCategoryIcon() -> String {
        switch recommendation.category {
        case .nutrition: return "leaf.fill"
        case .exercise: return "figure.walk"
        case .health: return "heart.fill"
        case .lifestyle: return "clock.fill"
        case .mental: return "brain.head.profile"
        }
    }
    
    private func getCategoryColor() -> Color {
        switch recommendation.category {
        case .nutrition: return .green
        case .exercise: return .blue
        case .health: return .red
        case .lifestyle: return .orange
        case .mental: return .purple
        }
    }
    
    private func getCategoryText() -> String {
        switch recommendation.category {
        case .nutrition: return "营养建议"
        case .exercise: return "运动建议"
        case .health: return "健康建议"
        case .lifestyle: return "生活方式"
        case .mental: return "心理健康"
        }
    }
}

// 优先级徽章
struct PriorityBadge: View {
    let priority: RecommendationPriority
    
    var body: some View {
        Text(getPriorityText())
            .font(.caption2)
            .fontWeight(.semibold)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(getPriorityColor())
            )
            .foregroundColor(.white)
    }
    
    private func getPriorityText() -> String {
        switch priority {
        case .low: return "低"
        case .medium: return "中"
        case .high: return "高"
        case .critical: return "紧急"
        }
    }
    
    private func getPriorityColor() -> Color {
        switch priority {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

#Preview {
    RecommendationsView()
        .environmentObject(HealthManager())
        .environmentObject(UserProfile())
} 