//
//  UserProfileService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation

class UserProfileService: ObservableObject {
    @Published var userProfile: UserProfile?
    
    private let userProfileKey = "user_profile"
    
    init() {
        loadUserProfile()
    }
    
    // 加载用户配置
    func loadUserProfile() {
        if let data = UserDefaults.standard.data(forKey: userProfileKey) {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            userProfile = try? decoder.decode(UserProfile.self, from: data)
        }
    }
    
    // 保存用户配置
    func saveUserProfile(_ profile: UserProfile) {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        if let data = try? encoder.encode(profile) {
            UserDefaults.standard.set(data, forKey: userProfileKey)
            userProfile = profile
        }
    }
    
    // 更新用户配置
    func updateUserProfile(
        name: String? = nil,
        age: Int? = nil,
        gender: Gender? = nil,
        height: Double? = nil,
        weight: Double? = nil,
        medicalHistory: [String]? = nil,
        allergies: [String]? = nil,
        medications: [String]? = nil,
        emergencyContact: String? = nil
    ) {
        var profile = userProfile ?? UserProfile()
        
        if let name = name { profile.name = name }
        if let age = age { profile.age = age }
        if let gender = gender { profile.gender = gender }
        if let height = height { profile.height = height }
        if let weight = weight { profile.weight = weight }
        if let medicalHistory = medicalHistory { profile.medicalHistory = medicalHistory }
        if let allergies = allergies { profile.allergies = allergies }
        if let medications = medications { profile.medications = medications }
        if let emergencyContact = emergencyContact { profile.emergencyContact = emergencyContact }
        
        profile.updatedAt = Date()
        saveUserProfile(profile)
    }
    
    // 创建新的用户配置
    func createUserProfile(
        name: String,
        age: Int,
        gender: Gender,
        height: Double? = nil,
        weight: Double? = nil
    ) {
        let profile = UserProfile(
            name: name,
            age: age,
            gender: gender,
            height: height,
            weight: weight
        )
        saveUserProfile(profile)
    }
    
    // 删除用户配置
    func deleteUserProfile() {
        UserDefaults.standard.removeObject(forKey: userProfileKey)
        userProfile = nil
    }
    
    // 计算BMI
    func calculateBMI() -> Double? {
        return userProfile?.bmi
    }
    
    // 获取BMI分类
    func getBMICategory() -> String {
        return userProfile?.bmiCategory ?? "未知"
    }
    
    // 获取年龄组
    func getAgeGroup() -> String {
        guard let age = userProfile?.age else { return "未知" }
        
        switch age {
        case 0..<18:
            return "未成年"
        case 18..<30:
            return "青年"
        case 30..<50:
            return "中年"
        case 50..<65:
            return "中老年"
        default:
            return "老年"
        }
    }
    
    // 获取健康风险因子
    func getHealthRiskFactors() -> [String] {
        guard let profile = userProfile else { return [] }
        
        var riskFactors: [String] = []
        
        // BMI风险
        if let bmi = profile.bmi {
            if bmi < 18.5 {
                riskFactors.append("体重偏轻")
            } else if bmi >= 28 {
                riskFactors.append("肥胖")
            } else if bmi >= 24 {
                riskFactors.append("超重")
            }
        }
        
        // 年龄风险
        if let age = profile.age, age >= 65 {
            riskFactors.append("高龄")
        }
        
        // 疾病史风险
        if !profile.medicalHistory.isEmpty {
            riskFactors.append("有疾病史")
        }
        
        // 过敏史
        if !profile.allergies.isEmpty {
            riskFactors.append("有过敏史")
        }
        
        // 用药情况
        if !profile.medications.isEmpty {
            riskFactors.append("正在用药")
        }
        
        return riskFactors
    }
    
    // 生成个性化建议
    func generatePersonalizedRecommendations() -> [String] {
        guard let profile = userProfile else { return ["请先完善个人信息"] }
        
        var recommendations: [String] = []
        
        // 基于BMI的建议
        if let bmi = profile.bmi {
            switch bmi {
            case ..<18.5:
                recommendations.append("建议增加营养摄入，适当增重")
            case 24..<28:
                recommendations.append("建议控制饮食，增加运动，减轻体重")
            case 28...:
                recommendations.append("建议咨询营养师，制定减重计划")
            default:
                recommendations.append("保持当前体重，继续健康生活方式")
            }
        }
        
        // 基于年龄的建议
        if let age = profile.age {
            switch age {
            case 18..<30:
                recommendations.append("年轻人应注重建立良好的生活习惯")
            case 30..<50:
                recommendations.append("中年人应注重工作生活平衡，定期体检")
            case 50..<65:
                recommendations.append("中老年人应加强慢性病预防，适度运动")
            case 65...:
                recommendations.append("老年人应注重安全，定期医疗检查")
            default:
                break
            }
        }
        
        // 基于性别的建议
        if let gender = profile.gender {
            switch gender {
            case .female:
                recommendations.append("女性应注重骨密度检查和妇科健康")
            case .male:
                recommendations.append("男性应注重心血管健康和前列腺检查")
            case .other:
                recommendations.append("建议根据个人情况制定健康计划")
            }
        }
        
        // 基于疾病史的建议
        if !profile.medicalHistory.isEmpty {
            recommendations.append("有疾病史者应定期复查，遵医嘱治疗")
        }
        
        // 基于过敏史的建议
        if !profile.allergies.isEmpty {
            recommendations.append("有过敏史者应避免接触过敏原")
        }
        
        return recommendations
    }
    
    // 检查信息完整性
    func getProfileCompleteness() -> Double {
        guard let profile = userProfile else { return 0.0 }
        
        var completedFields = 0
        let totalFields = 9
        
        if profile.name != nil { completedFields += 1 }
        if profile.age != nil { completedFields += 1 }
        if profile.gender != nil { completedFields += 1 }
        if profile.height != nil { completedFields += 1 }
        if profile.weight != nil { completedFields += 1 }
        if !profile.medicalHistory.isEmpty { completedFields += 1 }
        if !profile.allergies.isEmpty { completedFields += 1 }
        if !profile.medications.isEmpty { completedFields += 1 }
        if profile.emergencyContact != nil { completedFields += 1 }
        
        return Double(completedFields) / Double(totalFields)
    }
    
    // 获取缺失信息提示
    func getMissingInfoPrompts() -> [String] {
        guard let profile = userProfile else {
            return ["请创建用户档案"]
        }
        
        var prompts: [String] = []
        
        if profile.name == nil { prompts.append("请填写姓名") }
        if profile.age == nil { prompts.append("请填写年龄") }
        if profile.gender == nil { prompts.append("请选择性别") }
        if profile.height == nil { prompts.append("请填写身高") }
        if profile.weight == nil { prompts.append("请填写体重") }
        if profile.emergencyContact == nil { prompts.append("请填写紧急联系人") }
        
        return prompts
    }
}
