//
//  AIDigitalDoctorService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation
import Combine
import SwiftUI

// MARK: - AI数字医生服务
class AIDigitalDoctorService: ObservableObject {
    @Published var isAnalyzing = false
    @Published var currentRecommendations: [HealthRecommendation] = []
    @Published var riskAssessment: RiskAssessment?
    @Published var personalizedPlan: PersonalizedHealthPlan?

    // 新增对话功能
    @Published var chatMessages: [ChatMessage] = []
    @Published var isTyping = false
    @Published var currentAnalysis: HealthAnalysisResult?
    @Published var realTimeData: RealTimeHealthData?

    // 集成新的服务
    private let conversationService = AIConversationService()
    private let personalizedRecommendationEngine = PersonalizedRecommendationEngine()
    private let advancedHealthAnalysisService = AdvancedHealthAnalysisService()
    private let healthTrendAnalysisService = HealthTrendAnalysisService()

    // 保留原有服务
    private let healthAnalyzer = HealthAnalyzer()
    private let recommendationEngine = RecommendationEngine()
    private let riskCalculator = RiskCalculator()
    
    // MARK: - 对话功能

    /// 发送消息给AI医生
    func sendMessage(_ message: String, userProfile: ExtendedUserProfile?) async {
        isTyping = true

        // 添加用户消息
        let userMessage = ChatMessage(
            id: UUID(),
            content: message,
            isFromUser: true,
            timestamp: Date(),
            messageType: .text
        )

        await MainActor.run {
            chatMessages.append(userMessage)
        }

        // 获取最近的健康数据
        let recentHealthData = generateMockHealthData()

        // 使用对话服务处理消息
        let aiResponse = await conversationService.processUserMessage(
            message,
            userProfile: userProfile,
            recentHealthData: recentHealthData
        )

        // 转换为ChatMessage格式
        let aiChatMessage = ChatMessage(
            id: UUID(),
            content: aiResponse.content,
            isFromUser: false,
            timestamp: aiResponse.timestamp,
            messageType: convertMessageType(aiResponse.messageType)
        )

        await MainActor.run {
            chatMessages.append(aiChatMessage)
            isTyping = false
        }
    }

    /// 获取智能健康分析
    func getIntelligentHealthAnalysis(for profile: ExtendedUserProfile) async -> HealthAnalysisResult {
        isAnalyzing = true
        defer { isAnalyzing = false }

        // 使用高级健康分析服务
        let cardiovascularRisk = advancedHealthAnalysisService.calculateFraminghamRisk(profile: profile)
        let diabetesRisk = advancedHealthAnalysisService.calculateDiabetesRisk(profile: profile)
        let cancerRisk = advancedHealthAnalysisService.calculateCancerRisk(profile: profile)
        let mentalHealthRisk = advancedHealthAnalysisService.calculateMentalHealthRisk(profile: profile)

        // 构建风险评估
        let riskAssessment = RiskAssessment(
            overallRiskLevel: determineOverallRisk([cardiovascularRisk, diabetesRisk, cancerRisk, mentalHealthRisk]),
            specificRisks: [cardiovascularRisk, diabetesRisk, cancerRisk, mentalHealthRisk],
            assessmentDate: Date(),
            nextAssessmentDate: Calendar.current.date(byAdding: .month, value: 3, to: Date()) ?? Date()
        )

        // 生成个性化推荐
        let mockTrendAnalysis: [HealthTrendAnalysisService.TrendAnalysisResult] = []
        let personalizedRecommendations = await personalizedRecommendationEngine.generatePersonalizedRecommendations(
            profile: profile,
            healthAnalysis: HealthAnalysisResult(
                overallScore: 75.0,
                riskAssessment: riskAssessment,
                lifestyleAnalysis: analyzeLifestyle(profile: profile),
                mentalHealthScore: assessMentalHealth(profile: profile),
                nutritionAnalysis: analyzeNutrition(profile: profile),
                fitnessAnalysis: analyzeFitness(profile: profile),
                recommendations: [],
                analysisDate: Date()
            ),
            trendAnalysis: mockTrendAnalysis
        )

        let result = HealthAnalysisResult(
            overallScore: calculateOverallScore(
                basic: calculateBasicHealthScore(profile: profile),
                mental: assessMentalHealth(profile: profile),
                lifestyle: analyzeLifestyle(profile: profile).score,
                nutrition: analyzeNutrition(profile: profile).score,
                fitness: analyzeFitness(profile: profile).score
            ),
            riskAssessment: riskAssessment,
            lifestyleAnalysis: analyzeLifestyle(profile: profile),
            mentalHealthScore: assessMentalHealth(profile: profile),
            nutritionAnalysis: analyzeNutrition(profile: profile),
            fitnessAnalysis: analyzeFitness(profile: profile),
            recommendations: personalizedRecommendations.map { convertToHealthRecommendation($0) },
            analysisDate: Date()
        )

        await MainActor.run {
            self.currentAnalysis = result
        }

        return result
    }

    /// 获取实时健康建议
    func getRealTimeHealthAdvice(for data: RealTimeHealthData) -> [InstantAdvice] {
        var advice: [InstantAdvice] = []

        // 血压监控
        if let bp = data.bloodPressure {
            if bp.systolic > 140 || bp.diastolic > 90 {
                advice.append(InstantAdvice(
                    type: .urgent,
                    category: .medical,
                    title: "血压偏高警告",
                    message: "您的血压读数偏高（\(bp.systolic)/\(bp.diastolic)），建议立即休息并联系医生",
                    action: "立即测量血压并记录，如持续偏高请就医"
                ))
            } else if bp.systolic < 90 || bp.diastolic < 60 {
                advice.append(InstantAdvice(
                    type: .warning,
                    category: .medical,
                    title: "血压偏低",
                    message: "您的血压读数偏低，请注意休息",
                    action: "避免突然站立，适当补充水分"
                ))
            }
        }

        // 心率监控
        if let heartRate = data.heartRate {
            if heartRate > 100 {
                advice.append(InstantAdvice(
                    type: .warning,
                    category: .physical,
                    title: "心率偏快",
                    message: "您的心率为\(heartRate)次/分钟，偏快",
                    action: "进行深呼吸练习，避免剧烈运动"
                ))
            } else if heartRate < 60 {
                advice.append(InstantAdvice(
                    type: .info,
                    category: .physical,
                    title: "心率偏慢",
                    message: "您的心率为\(heartRate)次/分钟，如无不适可继续观察",
                    action: "如有头晕等症状请及时就医"
                ))
            }
        }

        return advice
    }

    /// 清除聊天记录
    func clearChatHistory() {
        chatMessages.removeAll()
        conversationService.clearConversationHistory()
    }

    /// 导出对话记录
    func exportChatHistory() -> String {
        return conversationService.exportConversationHistory()
    }

    // MARK: - 主要功能（原有）

    /// 综合健康分析
    func analyzeUserHealth(profile: ExtendedUserProfile) async -> HealthAnalysisResult {
        isAnalyzing = true
        defer { isAnalyzing = false }
        
        // 1. 基础健康指标分析
        let basicHealthScore = calculateBasicHealthScore(profile: profile)
        
        // 2. 疾病风险评估
        let riskAssessment = await assessHealthRisks(profile: profile)
        
        // 3. 生活方式分析
        let lifestyleAnalysis = analyzeLifestyle(profile: profile)
        
        // 4. 心理健康评估
        let mentalHealthScore = assessMentalHealth(profile: profile)
        
        // 5. 营养状况分析
        let nutritionAnalysis = analyzeNutrition(profile: profile)
        
        // 6. 运动健康评估
        let fitnessAnalysis = analyzeFitness(profile: profile)
        
        // 7. 生成个性化建议
        let recommendations = await generatePersonalizedRecommendations(
            profile: profile,
            riskAssessment: riskAssessment,
            lifestyleAnalysis: lifestyleAnalysis
        )
        
        let result = HealthAnalysisResult(
            overallScore: calculateOverallScore(
                basic: basicHealthScore,
                mental: mentalHealthScore,
                lifestyle: lifestyleAnalysis.score,
                nutrition: nutritionAnalysis.score,
                fitness: fitnessAnalysis.score
            ),
            riskAssessment: riskAssessment,
            lifestyleAnalysis: lifestyleAnalysis,
            mentalHealthScore: mentalHealthScore,
            nutritionAnalysis: nutritionAnalysis,
            fitnessAnalysis: fitnessAnalysis,
            recommendations: recommendations,
            analysisDate: Date()
        )
        
        await MainActor.run {
            self.currentRecommendations = recommendations
            self.riskAssessment = riskAssessment
        }
        
        return result
    }
    
    /// 生成个性化健康计划
    func generatePersonalizedPlan(for profile: ExtendedUserProfile) async -> PersonalizedHealthPlan {
        let analysis = await analyzeUserHealth(profile: profile)
        
        let dietPlan = generateDietPlan(profile: profile, analysis: analysis)
        let exercisePlan = generateExercisePlan(profile: profile, analysis: analysis)
        let lifestylePlan = generateLifestylePlan(profile: profile, analysis: analysis)
        let mentalHealthPlan = generateMentalHealthPlan(profile: profile, analysis: analysis)
        
        let plan = PersonalizedHealthPlan(
            userId: profile.id,
            dietPlan: dietPlan,
            exercisePlan: exercisePlan,
            lifestylePlan: lifestylePlan,
            mentalHealthPlan: mentalHealthPlan,
            goals: generateHealthGoals(profile: profile, analysis: analysis),
            timeline: generateTimeline(analysis: analysis),
            createdDate: Date()
        )
        
        await MainActor.run {
            self.personalizedPlan = plan
        }
        
        return plan
    }
    
    /// 实时健康监控和建议
    func provideRealTimeAdvice(for currentData: RealTimeHealthData) -> [InstantAdvice] {
        var advice: [InstantAdvice] = []
        
        // 血压监控
        if let bp = currentData.bloodPressure {
            if bp.systolic > 140 || bp.diastolic > 90 {
                advice.append(InstantAdvice(
                    type: .urgent,
                    category: .medical,
                    title: "血压偏高警告",
                    message: "您的血压读数偏高，建议立即休息并联系医生",
                    action: "立即测量血压并记录，如持续偏高请就医"
                ))
            }
        }
        
        // 心率监控
        if let heartRate = currentData.heartRate {
            if heartRate > 100 {
                advice.append(InstantAdvice(
                    type: .warning,
                    category: .physical,
                    title: "心率偏快",
                    message: "您的心率偏快，建议深呼吸放松",
                    action: "进行深呼吸练习，避免剧烈运动"
                ))
            }
        }
        
        // 血糖监控
        if let bloodSugar = currentData.bloodSugar {
            if bloodSugar < 70 {
                advice.append(InstantAdvice(
                    type: .urgent,
                    category: .medical,
                    title: "低血糖警告",
                    message: "血糖偏低，请立即补充糖分",
                    action: "立即食用含糖食物或饮料"
                ))
            } else if bloodSugar > 180 {
                advice.append(InstantAdvice(
                    type: .warning,
                    category: .medical,
                    title: "血糖偏高",
                    message: "血糖偏高，建议检查饮食和药物",
                    action: "检查最近饮食，确认药物服用情况"
                ))
            }
        }
        
        // 步数和活动监控
        if let steps = currentData.dailySteps, steps < 5000 {
            advice.append(InstantAdvice(
                type: .suggestion,
                category: .exercise,
                title: "增加日常活动",
                message: "今日步数较少，建议增加一些轻度活动",
                action: "尝试散步10-15分钟或做一些伸展运动"
            ))
        }
        
        // 睡眠监控
        if let sleepHours = currentData.lastNightSleep, sleepHours < 6 {
            advice.append(InstantAdvice(
                type: .warning,
                category: .lifestyle,
                title: "睡眠不足",
                message: "昨晚睡眠时间不足，今日注意休息",
                action: "今晚尽量早睡，避免咖啡因摄入"
            ))
        }
        
        return advice
    }
    
    // MARK: - 私有方法
    
    private func calculateBasicHealthScore(profile: ExtendedUserProfile) -> Double {
        var score: Double = 100
        
        // BMI评估
        let height = profile.basicInfo.height / 100 // 转换为米
        let weight = profile.basicInfo.weight
        if height > 0 && weight > 0 {
            let bmi = weight / (height * height)
            switch bmi {
            case ..<18.5: score -= 15 // 偏瘦
            case 18.5..<24: score += 0 // 正常
            case 24..<28: score -= 10 // 超重
            case 28...: score -= 25 // 肥胖
            default: break
            }
        }
        
        // 年龄因素
        let age = profile.basicInfo.age
        if age > 65 { score -= 5 }
        if age > 75 { score -= 10 }
        
        // 慢性疾病
        score -= Double(profile.medicalHistory.chronicDiseases.count * 10)
        
        // 血压评估
        let bp = profile.medicalHistory.bloodPressure
        switch bp.level {
        case .normal: break
        case .elevated: score -= 5
        case .stage1Hypertension: score -= 15
        case .stage2Hypertension: score -= 25
        case .low: score -= 10
        }
        
        return max(0, min(100, score))
    }
    
    private func assessHealthRisks(profile: ExtendedUserProfile) async -> RiskAssessment {
        var risks: [HealthRisk] = []
        
        // 心血管疾病风险
        let cvdRisk = calculateCardiovascularRisk(profile: profile)
        if cvdRisk.level != .low {
            risks.append(cvdRisk)
        }
        
        // 糖尿病风险
        let diabetesRisk = calculateDiabetesRisk(profile: profile)
        if diabetesRisk.level != .low {
            risks.append(diabetesRisk)
        }
        
        // 癌症风险
        let cancerRisk = calculateCancerRisk(profile: profile)
        if cancerRisk.level != .low {
            risks.append(cancerRisk)
        }
        
        // 心理健康风险
        let mentalHealthRisk = calculateMentalHealthRisk(profile: profile)
        if mentalHealthRisk.level != .low {
            risks.append(mentalHealthRisk)
        }
        
        return RiskAssessment(
            overallRiskLevel: calculateOverallRiskLevel(risks: risks),
            specificRisks: risks,
            assessmentDate: Date(),
            nextAssessmentDate: Calendar.current.date(byAdding: .month, value: 3, to: Date()) ?? Date()
        )
    }
    
    private func analyzeLifestyle(profile: ExtendedUserProfile) -> LifestyleAnalysis {
        var score: Double = 100
        var issues: [LifestyleIssue] = []
        var strengths: [LifestyleStrength] = []
        
        // 睡眠分析
        let sleepHours = profile.lifestyle.sleepPattern.averageSleepHours
        if sleepHours < 6 {
            score -= 20
            issues.append(LifestyleIssue(
                category: .sleep,
                severity: .high,
                description: "睡眠时间严重不足",
                recommendation: "建议每晚保证7-9小时睡眠"
            ))
        } else if sleepHours >= 7 && sleepHours <= 9 {
            strengths.append(LifestyleStrength(
                category: .sleep,
                description: "睡眠时间充足"
            ))
        }
        
        // 吸烟分析
        switch profile.lifestyle.smokingHistory.status {
        case .never:
            strengths.append(LifestyleStrength(category: .smoking, description: "无吸烟史"))
        case .former:
            strengths.append(LifestyleStrength(category: .smoking, description: "已成功戒烟"))
        case .occasional:
            score -= 10
            issues.append(LifestyleIssue(
                category: .smoking,
                severity: .moderate,
                description: "偶尔吸烟",
                recommendation: "建议完全戒烟"
            ))
        case .regular, .heavy:
            score -= 30
            issues.append(LifestyleIssue(
                category: .smoking,
                severity: .high,
                description: "吸烟习惯",
                recommendation: "强烈建议戒烟，寻求专业帮助"
            ))
        }
        
        return LifestyleAnalysis(
            score: max(0, score),
            issues: issues,
            strengths: strengths,
            recommendations: generateLifestyleRecommendations(issues: issues)
        )
    }
    
    private func assessMentalHealth(profile: ExtendedUserProfile) -> Double {
        var score: Double = 100
        
        // 情绪状态评估
        switch profile.mentalHealth.currentMood {
        case .veryPositive: score += 10
        case .positive: score += 5
        case .neutral: break
        case .negative: score -= 15
        case .veryNegative: score -= 30
        }
        
        // 焦虑水平评估
        switch profile.mentalHealth.anxietyLevel {
        case .none: break
        case .low: score -= 5
        case .moderate: score -= 15
        case .high: score -= 25
        case .severe: score -= 40
        }
        
        // 压力水平评估
        switch profile.mentalHealth.stressLevel {
        case .veryLow, .low: break
        case .moderate: score -= 10
        case .high: score -= 20
        case .veryHigh: score -= 35
        }
        
        return max(0, min(100, score))
    }
    
    private func analyzeNutrition(profile: ExtendedUserProfile) -> NutritionAnalysis {
        // 营养分析逻辑
        return NutritionAnalysis(
            score: 75,
            deficiencies: [],
            excesses: [],
            recommendations: []
        )
    }
    
    private func analyzeFitness(profile: ExtendedUserProfile) -> FitnessAnalysis {
        // 健身分析逻辑
        return FitnessAnalysis(
            score: 70,
            strengths: [],
            weaknesses: [],
            recommendations: []
        )
    }
    
    private func calculateOverallScore(basic: Double, mental: Double, lifestyle: Double, nutrition: Double, fitness: Double) -> Double {
        return (basic * 0.3 + mental * 0.2 + lifestyle * 0.2 + nutrition * 0.15 + fitness * 0.15)
    }

    // MARK: - 辅助方法

    /// 转换消息类型
    private func convertMessageType(_ type: AIConversationService.ConversationMessage.MessageType) -> ChatMessage.MessageType {
        switch type {
        case .text:
            return .text
        case .symptomReport:
            return .symptomReport
        case .healthData:
            return .healthData
        case .recommendation:
            return .recommendation
        case .warning:
            return .warning
        case .followUp:
            return .followUp
        case .emergency:
            return .emergency
        }
    }

    /// 转换为HealthRecommendation格式
    private func convertToHealthRecommendation(_ recommendation: PersonalizedRecommendationEngine.PersonalizedRecommendation) -> HealthRecommendation {
        return HealthRecommendation(
            id: recommendation.id,
            category: convertRecommendationCategory(recommendation.category),
            title: recommendation.title,
            description: recommendation.description,
            priority: convertPriority(recommendation.priority),
            actionSteps: recommendation.actionSteps.map { $0.description },
            expectedBenefit: recommendation.expectedBenefit,
            timeframe: recommendation.estimatedTime,
            isCompleted: false,
            createdDate: recommendation.createdDate
        )
    }

    /// 转换推荐类别
    private func convertRecommendationCategory(_ category: PersonalizedRecommendationEngine.PersonalizedRecommendation.RecommendationCategory) -> HealthRecommendation.Category {
        switch category {
        case .nutrition:
            return .nutrition
        case .exercise:
            return .exercise
        case .sleep:
            return .lifestyle
        case .stress:
            return .mentalHealth
        case .medical:
            return .medical
        case .lifestyle:
            return .lifestyle
        case .mental:
            return .mentalHealth
        }
    }

    /// 转换优先级
    private func convertPriority(_ priority: PersonalizedRecommendationEngine.PersonalizedRecommendation.Priority) -> HealthRecommendation.Priority {
        switch priority {
        case .urgent:
            return .urgent
        case .high:
            return .high
        case .medium:
            return .medium
        case .low:
            return .low
        }
    }

    /// 确定整体风险等级
    private func determineOverallRisk(_ risks: [HealthRisk]) -> RiskLevel {
        let highRiskCount = risks.filter { $0.level == .high }.count
        let moderateRiskCount = risks.filter { $0.level == .moderate }.count

        if highRiskCount > 0 {
            return .high
        } else if moderateRiskCount > 1 {
            return .moderate
        } else if moderateRiskCount > 0 {
            return .moderate
        } else {
            return .low
        }
    }

    /// 生成模拟健康数据
    private func generateMockHealthData() -> [HealthDataEntry] {
        let calendar = Calendar.current
        var entries: [HealthDataEntry] = []

        for i in 0..<7 {
            if let date = calendar.date(byAdding: .day, value: -i, to: Date()) {
                entries.append(HealthDataEntry(
                    id: UUID(),
                    type: .bloodPressure,
                    value: Double.random(in: 110...130),
                    unit: "mmHg",
                    timestamp: date,
                    notes: nil
                ))

                entries.append(HealthDataEntry(
                    id: UUID(),
                    type: .heartRate,
                    value: Double.random(in: 65...85),
                    unit: "bpm",
                    timestamp: date,
                    notes: nil
                ))

                entries.append(HealthDataEntry(
                    id: UUID(),
                    type: .weight,
                    value: Double.random(in: 60...80),
                    unit: "kg",
                    timestamp: date,
                    notes: nil
                ))
            }
        }

        return entries
    }

    /// 生成个性化推荐（原有方法的增强版本）
    private func generatePersonalizedRecommendations(
        profile: ExtendedUserProfile,
        riskAssessment: RiskAssessment,
        lifestyleAnalysis: LifestyleAnalysis
    ) async -> [HealthRecommendation] {

        var recommendations: [HealthRecommendation] = []

        // 基于风险评估的推荐
        for risk in riskAssessment.specificRisks {
            if risk.level == .high {
                recommendations.append(HealthRecommendation(
                    id: UUID(),
                    category: .medical,
                    title: "高风险疾病预防",
                    description: "您的\(risk.condition)风险较高，建议采取预防措施",
                    priority: .urgent,
                    actionSteps: ["定期体检", "调整生活方式", "咨询专科医生"],
                    expectedBenefit: "降低疾病发生风险",
                    timeframe: "立即开始",
                    isCompleted: false,
                    createdDate: Date()
                ))
            }
        }

        // 基于生活方式的推荐
        for issue in lifestyleAnalysis.issues {
            recommendations.append(HealthRecommendation(
                id: UUID(),
                category: .lifestyle,
                title: "生活方式改善",
                description: issue.description,
                priority: issue.severity == .high ? .high : .medium,
                actionSteps: [issue.recommendation],
                expectedBenefit: "改善整体健康状况",
                timeframe: "2-4周",
                isCompleted: false,
                createdDate: Date()
            ))
        }

        return recommendations
    }
}
