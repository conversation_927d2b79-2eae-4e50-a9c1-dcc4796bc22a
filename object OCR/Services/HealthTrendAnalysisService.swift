//
//  HealthTrendAnalysisService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation
import SwiftUI

/// 健康趋势分析服务 - 分析用户健康数据的变化趋势
class HealthTrendAnalysisService: ObservableObject {
    
    // MARK: - 趋势分析结果
    struct TrendAnalysisResult {
        let metric: String
        let trend: TrendDirection
        let changeRate: Double
        let significance: TrendSignificance
        let timeframe: String
        let recommendations: [String]
    }
    
    enum TrendDirection {
        case improving
        case stable
        case declining
        case fluctuating
        
        var displayName: String {
            switch self {
            case .improving: return "改善"
            case .stable: return "稳定"
            case .declining: return "下降"
            case .fluctuating: return "波动"
            }
        }
        
        var color: Color {
            switch self {
            case .improving: return .green
            case .stable: return .blue
            case .declining: return .red
            case .fluctuating: return .orange
            }
        }
    }
    
    enum TrendSignificance {
        case high
        case moderate
        case low
        
        var displayName: String {
            switch self {
            case .high: return "显著"
            case .moderate: return "中等"
            case .low: return "轻微"
            }
        }
    }
    
    // MARK: - 主要分析方法
    
    /// 分析体重趋势
    func analyzeWeightTrend(weightData: [HealthDataEntry]) -> TrendAnalysisResult {
        let weights = weightData.compactMap { entry -> (Date, Double)? in
            if case .weight(let value) = entry.value {
                return (entry.date, value)
            }
            return nil
        }.sorted { $0.0 < $1.0 }
        
        guard weights.count >= 3 else {
            return TrendAnalysisResult(
                metric: "体重",
                trend: .stable,
                changeRate: 0.0,
                significance: .low,
                timeframe: "数据不足",
                recommendations: ["建议增加体重记录频率以获得更准确的趋势分析"]
            )
        }
        
        let firstWeight = weights.first!.1
        let lastWeight = weights.last!.1
        let changeRate = ((lastWeight - firstWeight) / firstWeight) * 100
        
        let trend: TrendDirection
        let significance: TrendSignificance
        
        if abs(changeRate) < 2 {
            trend = .stable
            significance = .low
        } else if changeRate > 0 {
            trend = .declining // 体重增加对健康可能不利
            significance = abs(changeRate) > 5 ? .high : .moderate
        } else {
            trend = .improving // 适度减重通常有益
            significance = abs(changeRate) > 5 ? .high : .moderate
        }
        
        let timeframe = calculateTimeframe(from: weights.first!.0, to: weights.last!.0)
        let recommendations = generateWeightRecommendations(trend: trend, changeRate: changeRate)
        
        return TrendAnalysisResult(
            metric: "体重",
            trend: trend,
            changeRate: changeRate,
            significance: significance,
            timeframe: timeframe,
            recommendations: recommendations
        )
    }
    
    /// 分析血压趋势
    func analyzeBloodPressureTrend(bpData: [HealthDataEntry]) -> TrendAnalysisResult {
        let bloodPressures = bpData.compactMap { entry -> (Date, Double, Double)? in
            if case .bloodPressure(let systolic, let diastolic) = entry.value {
                return (entry.date, systolic, diastolic)
            }
            return nil
        }.sorted { $0.0 < $1.0 }
        
        guard bloodPressures.count >= 3 else {
            return TrendAnalysisResult(
                metric: "血压",
                trend: .stable,
                changeRate: 0.0,
                significance: .low,
                timeframe: "数据不足",
                recommendations: ["建议定期监测血压以跟踪健康状况"]
            )
        }
        
        // 分析收缩压趋势
        let systolicValues = bloodPressures.map { $0.1 }
        let systolicTrend = calculateLinearTrend(values: systolicValues)
        
        // 分析舒张压趋势
        let diastolicValues = bloodPressures.map { $0.2 }
        let diastolicTrend = calculateLinearTrend(values: diastolicValues)
        
        // 综合评估
        let avgTrend = (systolicTrend + diastolicTrend) / 2
        let trend: TrendDirection
        let significance: TrendSignificance
        
        if abs(avgTrend) < 2 {
            trend = .stable
            significance = .low
        } else if avgTrend > 0 {
            trend = .declining // 血压升高不利
            significance = abs(avgTrend) > 5 ? .high : .moderate
        } else {
            trend = .improving // 血压降低有利
            significance = abs(avgTrend) > 5 ? .high : .moderate
        }
        
        let timeframe = calculateTimeframe(from: bloodPressures.first!.0, to: bloodPressures.last!.0)
        let recommendations = generateBloodPressureRecommendations(trend: trend, avgTrend: avgTrend)
        
        return TrendAnalysisResult(
            metric: "血压",
            trend: trend,
            changeRate: avgTrend,
            significance: significance,
            timeframe: timeframe,
            recommendations: recommendations
        )
    }
    
    /// 分析血糖趋势
    func analyzeBloodSugarTrend(sugarData: [HealthDataEntry]) -> TrendAnalysisResult {
        let bloodSugars = sugarData.compactMap { entry -> (Date, Double)? in
            if case .bloodSugar(let value) = entry.value {
                return (entry.date, value)
            }
            return nil
        }.sorted { $0.0 < $1.0 }
        
        guard bloodSugars.count >= 3 else {
            return TrendAnalysisResult(
                metric: "血糖",
                trend: .stable,
                changeRate: 0.0,
                significance: .low,
                timeframe: "数据不足",
                recommendations: ["建议定期监测血糖水平"]
            )
        }
        
        let values = bloodSugars.map { $0.1 }
        let trendValue = calculateLinearTrend(values: values)
        
        let trend: TrendDirection
        let significance: TrendSignificance
        
        if abs(trendValue) < 0.5 {
            trend = .stable
            significance = .low
        } else if trendValue > 0 {
            trend = .declining // 血糖升高不利
            significance = abs(trendValue) > 1.0 ? .high : .moderate
        } else {
            trend = .improving // 血糖降低有利（在正常范围内）
            significance = abs(trendValue) > 1.0 ? .high : .moderate
        }
        
        let timeframe = calculateTimeframe(from: bloodSugars.first!.0, to: bloodSugars.last!.0)
        let recommendations = generateBloodSugarRecommendations(trend: trend, values: values)
        
        return TrendAnalysisResult(
            metric: "血糖",
            trend: trend,
            changeRate: trendValue,
            significance: significance,
            timeframe: timeframe,
            recommendations: recommendations
        )
    }
    
    /// 分析运动趋势
    func analyzeExerciseTrend(exerciseData: [HealthDataEntry]) -> TrendAnalysisResult {
        let exercises = exerciseData.compactMap { entry -> (Date, Double)? in
            if case .exercise(let duration, _) = entry.value {
                return (entry.date, duration)
            }
            return nil
        }.sorted { $0.0 < $1.0 }
        
        guard exercises.count >= 3 else {
            return TrendAnalysisResult(
                metric: "运动时长",
                trend: .stable,
                changeRate: 0.0,
                significance: .low,
                timeframe: "数据不足",
                recommendations: ["建议记录运动数据以跟踪健身进展"]
            )
        }
        
        let values = exercises.map { $0.1 }
        let trendValue = calculateLinearTrend(values: values)
        
        let trend: TrendDirection
        let significance: TrendSignificance
        
        if abs(trendValue) < 5 {
            trend = .stable
            significance = .low
        } else if trendValue > 0 {
            trend = .improving // 运动时长增加有利
            significance = abs(trendValue) > 15 ? .high : .moderate
        } else {
            trend = .declining // 运动时长减少不利
            significance = abs(trendValue) > 15 ? .high : .moderate
        }
        
        let timeframe = calculateTimeframe(from: exercises.first!.0, to: exercises.last!.0)
        let recommendations = generateExerciseRecommendations(trend: trend, avgDuration: values.reduce(0, +) / Double(values.count))
        
        return TrendAnalysisResult(
            metric: "运动时长",
            trend: trend,
            changeRate: trendValue,
            significance: significance,
            timeframe: timeframe,
            recommendations: recommendations
        )
    }
    
    /// 分析睡眠趋势
    func analyzeSleepTrend(sleepData: [HealthDataEntry]) -> TrendAnalysisResult {
        let sleeps = sleepData.compactMap { entry -> (Date, Double)? in
            if case .sleep(let hours, _) = entry.value {
                return (entry.date, hours)
            }
            return nil
        }.sorted { $0.0 < $1.0 }
        
        guard sleeps.count >= 3 else {
            return TrendAnalysisResult(
                metric: "睡眠时长",
                trend: .stable,
                changeRate: 0.0,
                significance: .low,
                timeframe: "数据不足",
                recommendations: ["建议记录睡眠数据以改善睡眠质量"]
            )
        }
        
        let values = sleeps.map { $0.1 }
        let trendValue = calculateLinearTrend(values: values)
        let avgSleep = values.reduce(0, +) / Double(values.count)
        
        let trend: TrendDirection
        let significance: TrendSignificance
        
        if abs(trendValue) < 0.5 {
            trend = .stable
            significance = .low
        } else {
            // 睡眠趋势的评估需要考虑当前平均睡眠时长
            if avgSleep < 7 && trendValue > 0 {
                trend = .improving // 睡眠不足时增加睡眠有利
            } else if avgSleep > 9 && trendValue < 0 {
                trend = .improving // 睡眠过多时减少睡眠有利
            } else if trendValue > 0 {
                trend = avgSleep > 8 ? .declining : .improving
            } else {
                trend = avgSleep < 7 ? .declining : .improving
            }
            
            significance = abs(trendValue) > 1.0 ? .high : .moderate
        }
        
        let timeframe = calculateTimeframe(from: sleeps.first!.0, to: sleeps.last!.0)
        let recommendations = generateSleepRecommendations(trend: trend, avgSleep: avgSleep)
        
        return TrendAnalysisResult(
            metric: "睡眠时长",
            trend: trend,
            changeRate: trendValue,
            significance: significance,
            timeframe: timeframe,
            recommendations: recommendations
        )
    }
    
    // MARK: - 辅助计算方法
    
    /// 计算线性趋势
    private func calculateLinearTrend(values: [Double]) -> Double {
        guard values.count >= 2 else { return 0.0 }
        
        let n = Double(values.count)
        let x = Array(0..<values.count).map { Double($0) }
        let y = values
        
        let sumX = x.reduce(0, +)
        let sumY = y.reduce(0, +)
        let sumXY = zip(x, y).map(*).reduce(0, +)
        let sumX2 = x.map { $0 * $0 }.reduce(0, +)
        
        let slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
        
        return slope
    }
    
    /// 计算时间框架
    private func calculateTimeframe(from startDate: Date, to endDate: Date) -> String {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day, .weekOfYear, .month], from: startDate, to: endDate)
        
        if let months = components.month, months > 0 {
            return "\(months)个月"
        } else if let weeks = components.weekOfYear, weeks > 0 {
            return "\(weeks)周"
        } else if let days = components.day, days > 0 {
            return "\(days)天"
        } else {
            return "1天"
        }
    }
    
    // MARK: - 建议生成方法
    
    private func generateWeightRecommendations(trend: TrendDirection, changeRate: Double) -> [String] {
        var recommendations: [String] = []
        
        switch trend {
        case .improving:
            if abs(changeRate) > 5 {
                recommendations.append("体重下降较快，请确保营养均衡，避免过度节食")
            } else {
                recommendations.append("体重控制良好，继续保持健康的饮食和运动习惯")
            }
        case .declining:
            recommendations.append("体重有上升趋势，建议增加运动量并控制饮食")
            recommendations.append("考虑咨询营养师制定个性化的减重计划")
        case .stable:
            recommendations.append("体重保持稳定，继续维持当前的生活方式")
        case .fluctuating:
            recommendations.append("体重波动较大，建议规律饮食和运动")
        }
        
        return recommendations
    }
    
    private func generateBloodPressureRecommendations(trend: TrendDirection, avgTrend: Double) -> [String] {
        var recommendations: [String] = []
        
        switch trend {
        case .improving:
            recommendations.append("血压控制良好，继续保持健康的生活方式")
        case .declining:
            recommendations.append("血压有上升趋势，建议减少盐分摄入")
            recommendations.append("增加有氧运动，每周至少150分钟")
            if abs(avgTrend) > 5 {
                recommendations.append("建议咨询医生，可能需要药物治疗")
            }
        case .stable:
            recommendations.append("血压保持稳定，继续监测")
        case .fluctuating:
            recommendations.append("血压波动较大，建议规律作息和减少压力")
        }
        
        return recommendations
    }
    
    private func generateBloodSugarRecommendations(trend: TrendDirection, values: [Double]) -> [String] {
        var recommendations: [String] = []
        let avgValue = values.reduce(0, +) / Double(values.count)
        
        switch trend {
        case .improving:
            recommendations.append("血糖控制良好，继续保持健康饮食")
        case .declining:
            recommendations.append("血糖有上升趋势，建议控制碳水化合物摄入")
            recommendations.append("增加体力活动，有助于血糖控制")
            if avgValue > 7.0 {
                recommendations.append("血糖水平偏高，建议咨询医生")
            }
        case .stable:
            recommendations.append("血糖保持稳定，继续监测")
        case .fluctuating:
            recommendations.append("血糖波动较大，建议规律饮食")
        }
        
        return recommendations
    }
    
    private func generateExerciseRecommendations(trend: TrendDirection, avgDuration: Double) -> [String] {
        var recommendations: [String] = []
        
        switch trend {
        case .improving:
            recommendations.append("运动量增加良好，注意循序渐进")
            if avgDuration > 90 {
                recommendations.append("运动时间较长，注意休息和恢复")
            }
        case .declining:
            recommendations.append("运动量有所减少，建议制定规律的运动计划")
            recommendations.append("可以从简单的活动开始，如散步、爬楼梯")
        case .stable:
            if avgDuration < 30 {
                recommendations.append("建议增加运动时间，每次至少30分钟")
            } else {
                recommendations.append("运动量保持稳定，很好！")
            }
        case .fluctuating:
            recommendations.append("运动频率不够规律，建议制定固定的运动时间")
        }
        
        return recommendations
    }
    
    private func generateSleepRecommendations(trend: TrendDirection, avgSleep: Double) -> [String] {
        var recommendations: [String] = []
        
        if avgSleep < 7 {
            recommendations.append("睡眠时间不足，建议每晚保证7-9小时睡眠")
        } else if avgSleep > 9 {
            recommendations.append("睡眠时间过长，可能影响睡眠质量")
        }
        
        switch trend {
        case .improving:
            recommendations.append("睡眠时间趋向合理，继续保持")
        case .declining:
            recommendations.append("睡眠质量有所下降，建议改善睡眠环境")
            recommendations.append("避免睡前使用电子设备")
        case .stable:
            recommendations.append("睡眠时间稳定，注意睡眠质量")
        case .fluctuating:
            recommendations.append("睡眠时间不规律，建议固定作息时间")
        }
        
        return recommendations
    }
}
