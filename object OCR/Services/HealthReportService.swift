//
//  HealthReportService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation

class HealthReportService: ObservableObject {
    @Published var reports: [HealthReport] = []
    
    private let reportsKey = "health_reports"
    
    init() {
        loadReports()
    }
    
    // 生成综合健康报告
    func generateComprehensiveReport(
        assessmentService: HealthAssessmentService,
        dataService: HealthDataService,
        profileService: UserProfileService
    ) -> HealthReport {
        let reportId = UUID()
        let generatedDate = Date()
        let reportPeriod = "最近30天"
        
        // 生成报告各部分内容
        let summary = generateSummary(
            assessmentService: assessmentService,
            dataService: dataService,
            profileService: profileService
        )
        
        let assessmentAnalysis = generateAssessmentAnalysis(assessmentService: assessmentService)
        let dataAnalysis = generateDataAnalysis(dataService: dataService)
        let goalAnalysis = generateGoalAnalysis(dataService: dataService)
        let recommendations = generateComprehensiveRecommendations(
            assessmentService: assessmentService,
            dataService: dataService,
            profileService: profileService
        )
        
        let report = HealthReport(
            title: "综合健康报告",
            generatedDate: generatedDate,
            reportPeriod: reportPeriod,
            summary: summary,
            assessmentAnalysis: assessmentAnalysis,
            dataAnalysis: dataAnalysis,
            goalAnalysis: goalAnalysis,
            recommendations: recommendations
        )
        
        // 保存报告
        reports.insert(report, at: 0)
        saveReports()
        
        return report
    }
    
    // 生成摘要
    private func generateSummary(
        assessmentService: HealthAssessmentService,
        dataService: HealthDataService,
        profileService: UserProfileService
    ) -> String {
        var summaryParts: [String] = []
        
        // 用户基本信息
        if let profile = profileService.userProfile {
            var profileInfo = "用户档案："
            if let name = profile.name { profileInfo += "姓名\(name)，" }
            if let age = profile.age { profileInfo += "年龄\(age)岁，" }
            if let gender = profile.gender { profileInfo += "性别\(gender.displayName)，" }
            if let bmi = profile.bmi { profileInfo += "BMI\(String(format: "%.1f", bmi))（\(profile.bmiCategory)）" }
            summaryParts.append(profileInfo)
        }
        
        // 最新评估结果
        if let latestAssessment = assessmentService.latestAssessment {
            let assessmentInfo = "最新健康评估：总分\(latestAssessment.totalScore)分，风险等级为\(latestAssessment.overallRiskLevel.displayName)"
            summaryParts.append(assessmentInfo)
        }
        
        // 数据记录情况
        let dataCount = dataService.healthData.count
        let goalCount = dataService.healthGoals.count
        let completedGoals = dataService.getCompletedGoals().count
        let dataInfo = "健康数据：共记录\(dataCount)条数据，设定\(goalCount)个目标，已完成\(completedGoals)个目标"
        summaryParts.append(dataInfo)
        
        return summaryParts.joined(separator: "\n\n")
    }
    
    // 生成评估分析
    private func generateAssessmentAnalysis(assessmentService: HealthAssessmentService) -> String {
        guard let latestAssessment = assessmentService.latestAssessment else {
            return "暂无健康评估数据"
        }
        
        var analysis: [String] = []
        
        // 总体评估
        analysis.append("总体健康评估：")
        analysis.append("• 总分：\(latestAssessment.totalScore)分")
        analysis.append("• 风险等级：\(latestAssessment.overallRiskLevel.displayName)")
        
        // 分类得分分析
        analysis.append("\n各健康维度得分：")
        let sortedScores = latestAssessment.categoryScores.sorted { $0.value > $1.value }
        
        for (category, score) in sortedScores {
            let status = score >= 10 ? "良好" : score >= 6 ? "一般" : "需要关注"
            analysis.append("• \(category.displayName)：\(score)分（\(status)）")
        }
        
        // 趋势分析
        let trend = assessmentService.getAssessmentTrend()
        if trend.count >= 2 {
            let latest = trend.last ?? 0
            let previous = trend[trend.count - 2]
            let change = latest - previous
            
            analysis.append("\n评估趋势：")
            if change > 0 {
                analysis.append("• 相比上次评估，总分提高了\(change)分，健康状况有所改善")
            } else if change < 0 {
                analysis.append("• 相比上次评估，总分降低了\(-change)分，需要关注健康状况")
            } else {
                analysis.append("• 相比上次评估，总分保持稳定")
            }
        }
        
        return analysis.joined(separator: "\n")
    }
    
    // 生成数据分析
    private func generateDataAnalysis(dataService: HealthDataService) -> String {
        let recentData = dataService.getRecentHealthData(days: 30)
        
        if recentData.isEmpty {
            return "最近30天内暂无健康数据记录"
        }
        
        var analysis: [String] = []
        
        analysis.append("健康数据分析（最近30天）：")
        analysis.append("• 总记录数：\(recentData.count)条")
        
        // 分类统计
        let categoryStats = dataService.getCategoryStatistics()
        let sortedStats = categoryStats.sorted { $0.value > $1.value }
        
        analysis.append("\n各类别记录数：")
        for (category, count) in sortedStats.prefix(5) {
            if count > 0 {
                analysis.append("• \(category.displayName)：\(count)条")
            }
        }
        
        // 记录频率分析
        let weeklyStats = dataService.getWeeklyStatistics()
        if !weeklyStats.isEmpty {
            let avgDaily = weeklyStats.values.reduce(0, +) / weeklyStats.count
            analysis.append("\n记录频率：")
            analysis.append("• 平均每日记录：\(avgDaily)条")
        }
        
        return analysis.joined(separator: "\n")
    }
    
    // 生成目标分析
    private func generateGoalAnalysis(dataService: HealthDataService) -> String {
        let allGoals = dataService.healthGoals
        
        if allGoals.isEmpty {
            return "暂未设定健康目标"
        }
        
        var analysis: [String] = []
        
        let activeGoals = dataService.getActiveGoals()
        let completedGoals = dataService.getCompletedGoals()
        let expiredGoals = dataService.getExpiredGoals()
        
        analysis.append("健康目标分析：")
        analysis.append("• 总目标数：\(allGoals.count)个")
        analysis.append("• 进行中：\(activeGoals.count)个")
        analysis.append("• 已完成：\(completedGoals.count)个")
        analysis.append("• 已过期：\(expiredGoals.count)个")
        
        let completionRate = dataService.getGoalCompletionRate()
        analysis.append("• 完成率：\(String(format: "%.1f", completionRate * 100))%")
        
        // 目标进度分析
        if !activeGoals.isEmpty {
            analysis.append("\n活跃目标进度：")
            for goal in activeGoals.prefix(3) {
                let progressPercent = goal.progress * 100
                analysis.append("• \(goal.title)：\(String(format: "%.1f", progressPercent))%")
            }
        }
        
        return analysis.joined(separator: "\n")
    }
    
    // 生成综合建议
    private func generateComprehensiveRecommendations(
        assessmentService: HealthAssessmentService,
        dataService: HealthDataService,
        profileService: UserProfileService
    ) -> [String] {
        var recommendations: [String] = []
        
        // 基于评估结果的建议
        if let latestAssessment = assessmentService.latestAssessment {
            recommendations.append(contentsOf: latestAssessment.recommendations.prefix(3))
        }
        
        // 基于用户档案的建议
        let profileRecommendations = profileService.generatePersonalizedRecommendations()
        recommendations.append(contentsOf: profileRecommendations.prefix(2))
        
        // 基于数据记录的建议
        let recentData = dataService.getRecentHealthData(days: 7)
        if recentData.count < 3 {
            recommendations.append("建议增加健康数据记录频率，每周至少记录3次")
        }
        
        // 基于目标完成情况的建议
        let completionRate = dataService.getGoalCompletionRate()
        if completionRate < 0.5 {
            recommendations.append("目标完成率较低，建议重新评估目标的可行性")
        }
        
        let expiredGoals = dataService.getExpiredGoals()
        if !expiredGoals.isEmpty {
            recommendations.append("有\(expiredGoals.count)个目标已过期，建议重新制定或调整目标")
        }
        
        // 去重并限制数量
        let uniqueRecommendations = Array(Set(recommendations))
        return Array(uniqueRecommendations.prefix(8))
    }
    
    // 生成文本报告
    func generateTextReport(_ report: HealthReport) -> String {
        var textReport: [String] = []
        
        textReport.append("=== \(report.title) ===")
        textReport.append("生成时间：\(formatDate(report.generatedDate))")
        textReport.append("报告周期：\(report.reportPeriod)")
        textReport.append("")
        
        textReport.append("【健康摘要】")
        textReport.append(report.summary)
        textReport.append("")
        
        textReport.append("【评估分析】")
        textReport.append(report.assessmentAnalysis)
        textReport.append("")
        
        textReport.append("【数据分析】")
        textReport.append(report.dataAnalysis)
        textReport.append("")
        
        textReport.append("【目标分析】")
        textReport.append(report.goalAnalysis)
        textReport.append("")
        
        textReport.append("【健康建议】")
        for (index, recommendation) in report.recommendations.enumerated() {
            textReport.append("\(index + 1). \(recommendation)")
        }
        
        return textReport.joined(separator: "\n")
    }
    
    // 删除报告
    func deleteReport(_ report: HealthReport) {
        reports.removeAll { $0.id == report.id }
        saveReports()
    }
    
    // 清空报告
    func clearReports() {
        reports.removeAll()
        saveReports()
    }
    
    // MARK: - 数据持久化
    
    private func loadReports() {
        if let data = UserDefaults.standard.data(forKey: reportsKey) {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            reports = (try? decoder.decode([HealthReport].self, from: data)) ?? []
        }
    }
    
    private func saveReports() {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        if let data = try? encoder.encode(reports) {
            UserDefaults.standard.set(data, forKey: reportsKey)
        }
    }
    
    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}
