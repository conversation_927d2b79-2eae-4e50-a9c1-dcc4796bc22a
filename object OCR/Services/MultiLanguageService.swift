//
//  MultiLanguageService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation
import SwiftUI

/// 多语言支持服务
class MultiLanguageService: ObservableObject {
    
    @Published var currentLanguage: Language = .chinese
    @Published var availableLanguages: [Language] = [.chinese, .uyghur, .english]
    
    // MARK: - 语言枚举
    
    enum Language: String, CaseIterable, Codable {
        case chinese = "zh-CN"
        case uyghur = "ug-CN"
        case english = "en-US"
        
        var displayName: String {
            switch self {
            case .chinese: return "中文"
            case .uyghur: return "ئۇيغۇرچە"
            case .english: return "English"
            }
        }
        
        var flag: String {
            switch self {
            case .chinese: return "🇨🇳"
            case .uyghur: return "🏳️"
            case .english: return "🇺🇸"
            }
        }
        
        var code: String {
            return self.rawValue
        }
    }
    
    // MARK: - 本地化字符串
    
    private let localizedStrings: [Language: [String: String]] = [
        .chinese: [
            // 通用
            "app_name": "智能健康管理",
            "welcome": "欢迎",
            "settings": "设置",
            "profile": "个人资料",
            "health_data": "健康数据",
            "recommendations": "建议",
            "analysis": "分析",
            "save": "保存",
            "cancel": "取消",
            "confirm": "确认",
            "delete": "删除",
            "edit": "编辑",
            "add": "添加",
            "back": "返回",
            "next": "下一步",
            "done": "完成",
            "loading": "加载中...",
            "error": "错误",
            "success": "成功",
            
            // 健康相关
            "health_score": "健康评分",
            "blood_pressure": "血压",
            "heart_rate": "心率",
            "blood_sugar": "血糖",
            "weight": "体重",
            "height": "身高",
            "bmi": "BMI",
            "exercise": "运动",
            "sleep": "睡眠",
            "nutrition": "营养",
            "mental_health": "心理健康",
            "stress_level": "压力水平",
            "mood": "情绪",
            
            // AI医生
            "ai_doctor": "AI医生",
            "chat_with_ai": "与AI对话",
            "health_analysis": "健康分析",
            "real_time_monitoring": "实时监控",
            "personalized_recommendations": "个性化建议",
            "risk_assessment": "风险评估",
            "health_plan": "健康计划",
            
            // 健康智慧
            "daily_wisdom": "每日智慧",
            "health_tips": "健康贴士",
            "traditional_wisdom": "传统智慧",
            "modern_science": "现代科学",
            
            // 状态和级别
            "low": "低",
            "medium": "中",
            "high": "高",
            "normal": "正常",
            "abnormal": "异常",
            "urgent": "紧急",
            "warning": "警告",
            "info": "信息",
            
            // 时间
            "today": "今天",
            "yesterday": "昨天",
            "this_week": "本周",
            "this_month": "本月",
            "last_week": "上周",
            "last_month": "上月",

            // 语言选择
            "select_language": "选择语言",
            "language_description": "选择您偏好的语言，应用将使用该语言显示界面和健康建议"
        ],
        
        .uyghur: [
            // 通用
            "app_name": "ئاقىللىق ساغلاملىق باشقۇرۇش",
            "welcome": "خۇش كەلدىڭىز",
            "settings": "تەڭشەكلەر",
            "profile": "شەخسىي ئۇچۇرلار",
            "health_data": "ساغلاملىق سانلىق مەلۇماتلىرى",
            "recommendations": "تەۋسىيەلەر",
            "analysis": "تەھلىل",
            "save": "ساقلاش",
            "cancel": "بىكار قىلىش",
            "confirm": "جەزملەشتۈرۈش",
            "delete": "ئۆچۈرۈش",
            "edit": "تەھرىرلەش",
            "add": "قوشۇش",
            "back": "قايتىش",
            "next": "كېيىنكى قەدەم",
            "done": "تامام",
            "loading": "يۈكلىنىۋاتىدۇ...",
            "error": "خاتالىق",
            "success": "مۇۋەپپەقىيەت",
            
            // 健康相关
            "health_score": "ساغلاملىق نومۇرى",
            "blood_pressure": "قان بېسىمى",
            "heart_rate": "يۈرەك ئۇرۇش سۈرئىتى",
            "blood_sugar": "قان شېكىرى",
            "weight": "ئېغىرلىق",
            "height": "بويى",
            "bmi": "BMI",
            "exercise": "ماشىق",
            "sleep": "ئۇخلاش",
            "nutrition": "ئوزۇقلۇق",
            "mental_health": "روھىي ساغلاملىق",
            "stress_level": "بېسىم دەرىجىسى",
            "mood": "كەيپىيات",
            
            // AI医生
            "ai_doctor": "AI دوختۇر",
            "chat_with_ai": "AI بىلەن سۆھبەت",
            "health_analysis": "ساغلاملىق تەھلىلى",
            "real_time_monitoring": "ھەقىقىي ۋاقىت كۆزىتىش",
            "personalized_recommendations": "شەخسىيلەشتۈرۈلگەن تەۋسىيەلەر",
            "risk_assessment": "خەتەر باھالاش",
            "health_plan": "ساغلاملىق پىلانى",
            
            // 健康智慧
            "daily_wisdom": "كۈندىلىك دانالىق",
            "health_tips": "ساغلاملىق مەسلىھەتلىرى",
            "traditional_wisdom": "ئەنئەنىۋى دانالىق",
            "modern_science": "زامانىۋى ئىلىم",
            
            // 状态和级别
            "low": "تۆۋەن",
            "medium": "ئوتتۇرا",
            "high": "يۇقىرى",
            "normal": "نورمال",
            "abnormal": "نورمالسىز",
            "urgent": "جىددىي",
            "warning": "ئاگاھلاندۇرۇش",
            "info": "ئۇچۇر",
            
            // 时间
            "today": "بۈگۈن",
            "yesterday": "تۈنۈگۈن",
            "this_week": "بۇ ھەپتە",
            "this_month": "بۇ ئاي",
            "last_week": "ئۆتكەن ھەپتە",
            "last_month": "ئۆتكەن ئاي",

            // 语言选择
            "select_language": "تىل تاللاش",
            "language_description": "سىز ياخشى كۆرىدىغان تىلنى تاللاڭ، ئەپ شۇ تىلدا كۆرسىتىلىدۇ"
        ],
        
        .english: [
            // 通用
            "app_name": "Intelligent Health Management",
            "welcome": "Welcome",
            "settings": "Settings",
            "profile": "Profile",
            "health_data": "Health Data",
            "recommendations": "Recommendations",
            "analysis": "Analysis",
            "save": "Save",
            "cancel": "Cancel",
            "confirm": "Confirm",
            "delete": "Delete",
            "edit": "Edit",
            "add": "Add",
            "back": "Back",
            "next": "Next",
            "done": "Done",
            "loading": "Loading...",
            "error": "Error",
            "success": "Success",
            
            // 健康相关
            "health_score": "Health Score",
            "blood_pressure": "Blood Pressure",
            "heart_rate": "Heart Rate",
            "blood_sugar": "Blood Sugar",
            "weight": "Weight",
            "height": "Height",
            "bmi": "BMI",
            "exercise": "Exercise",
            "sleep": "Sleep",
            "nutrition": "Nutrition",
            "mental_health": "Mental Health",
            "stress_level": "Stress Level",
            "mood": "Mood",
            
            // AI医生
            "ai_doctor": "AI Doctor",
            "chat_with_ai": "Chat with AI",
            "health_analysis": "Health Analysis",
            "real_time_monitoring": "Real-time Monitoring",
            "personalized_recommendations": "Personalized Recommendations",
            "risk_assessment": "Risk Assessment",
            "health_plan": "Health Plan",
            
            // 健康智慧
            "daily_wisdom": "Daily Wisdom",
            "health_tips": "Health Tips",
            "traditional_wisdom": "Traditional Wisdom",
            "modern_science": "Modern Science",
            
            // 状态和级别
            "low": "Low",
            "medium": "Medium",
            "high": "High",
            "normal": "Normal",
            "abnormal": "Abnormal",
            "urgent": "Urgent",
            "warning": "Warning",
            "info": "Info",
            
            // 时间
            "today": "Today",
            "yesterday": "Yesterday",
            "this_week": "This Week",
            "this_month": "This Month",
            "last_week": "Last Week",
            "last_month": "Last Month",

            // 语言选择
            "select_language": "Select Language",
            "language_description": "Choose your preferred language. The app will display interface and health advice in this language."
        ]
    ]
    
    // MARK: - 公共方法
    
    /// 获取本地化字符串
    func localizedString(for key: String, language: Language? = nil) -> String {
        let lang = language ?? currentLanguage
        return localizedStrings[lang]?[key] ?? key
    }
    
    /// 切换语言
    func switchLanguage(to language: Language) {
        currentLanguage = language
        UserDefaults.standard.set(language.rawValue, forKey: "selected_language")
        
        // 发送语言变更通知
        NotificationCenter.default.post(
            name: .languageDidChange,
            object: nil,
            userInfo: ["language": language]
        )
    }
    
    /// 从UserDefaults加载保存的语言设置
    func loadSavedLanguage() {
        if let savedLanguageCode = UserDefaults.standard.string(forKey: "selected_language"),
           let savedLanguage = Language(rawValue: savedLanguageCode) {
            currentLanguage = savedLanguage
        }
    }
    
    /// 获取当前语言的显示名称
    var currentLanguageDisplayName: String {
        return currentLanguage.displayName
    }
    
    /// 获取当前语言的旗帜图标
    var currentLanguageFlag: String {
        return currentLanguage.flag
    }
    
    /// 检查是否支持指定语言
    func isLanguageSupported(_ language: Language) -> Bool {
        return availableLanguages.contains(language)
    }
    
    /// 获取所有支持的语言
    func getSupportedLanguages() -> [Language] {
        return availableLanguages
    }
    
    /// 格式化日期为当前语言
    func formatDate(_ date: Date, style: DateFormatter.Style = .medium) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = style
        formatter.locale = Locale(identifier: currentLanguage.code)
        return formatter.string(from: date)
    }
    
    /// 格式化数字为当前语言
    func formatNumber(_ number: Double, style: NumberFormatter.Style = .decimal) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = style
        formatter.locale = Locale(identifier: currentLanguage.code)
        return formatter.string(from: NSNumber(value: number)) ?? "\(number)"
    }
    
    /// 获取健康建议的本地化文本
    func getLocalizedHealthAdvice(for category: String, advice: String) -> String {
        // 这里可以根据不同语言提供不同的健康建议
        switch currentLanguage {
        case .chinese:
            return advice
        case .uyghur:
            return translateToUyghur(advice)
        case .english:
            return translateToEnglish(advice)
        }
    }
    
    // MARK: - 私有方法
    
    private func translateToUyghur(_ text: String) -> String {
        // 简化的维吾尔语翻译逻辑
        // 实际应用中应该使用专业的翻译服务
        let translations: [String: String] = [
            "建议多喝水": "سۇ كۆپ ئىچىشنى تەۋسىيە قىلىمىز",
            "注意休息": "ئارام ئېلىشقا دىققەت قىلىڭ",
            "适量运动": "مۇۋاپىق ماشىق قىلىڭ",
            "均衡饮食": "تەڭپۇڭ يېمەكلىك",
            "保持心情愉快": "كەيپىياتنى خۇش تۇتۇڭ"
        ]
        
        return translations[text] ?? text
    }
    
    private func translateToEnglish(_ text: String) -> String {
        // 简化的英语翻译逻辑
        let translations: [String: String] = [
            "建议多喝水": "It is recommended to drink more water",
            "注意休息": "Pay attention to rest",
            "适量运动": "Exercise in moderation",
            "均衡饮食": "Balanced diet",
            "保持心情愉快": "Keep a happy mood"
        ]
        
        return translations[text] ?? text
    }
}

// MARK: - 通知扩展

extension Notification.Name {
    static let languageDidChange = Notification.Name("languageDidChange")
}

// MARK: - 本地化字符串便捷方法

extension String {
    func localized(using service: MultiLanguageService) -> String {
        return service.localizedString(for: self)
    }
}

// MARK: - SwiftUI环境值

struct LanguageKey: EnvironmentKey {
    static let defaultValue: MultiLanguageService.Language = .chinese
}

extension EnvironmentValues {
    var currentLanguage: MultiLanguageService.Language {
        get { self[LanguageKey.self] }
        set { self[LanguageKey.self] = newValue }
    }
}
