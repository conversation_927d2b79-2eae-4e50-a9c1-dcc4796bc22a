//
//  HealthDataService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation

class HealthDataService: ObservableObject {
    @Published var healthData: [HealthDataEntry] = []
    @Published var healthGoals: [HealthGoal] = []
    @Published var healthReminders: [HealthReminder] = []
    
    private let healthDataKey = "health_data"
    private let healthGoalsKey = "health_goals"
    private let healthRemindersKey = "health_reminders"
    
    init() {
        loadHealthData()
        loadHealthGoals()
        loadHealthReminders()
    }
    
    // MARK: - 健康数据管理
    
    // 添加健康数据
    func addHealthData(category: HealthCategory, value: Double, unit: String, notes: String? = nil) {
        let entry = HealthDataEntry(category: category, value: value, unit: unit, notes: notes)
        healthData.insert(entry, at: 0)
        saveHealthData()
    }
    
    // 删除健康数据
    func deleteHealthData(_ entry: HealthDataEntry) {
        healthData.removeAll { $0.id == entry.id }
        saveHealthData()
    }
    
    // 获取指定分类的数据
    func getHealthData(for category: HealthCategory) -> [HealthDataEntry] {
        return healthData.filter { $0.category == category }
    }
    
    // 获取最近的数据
    func getRecentHealthData(days: Int = 7) -> [HealthDataEntry] {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        return healthData.filter { $0.recordedAt >= cutoffDate }
    }
    
    // 获取数据趋势
    func getDataTrend(for category: HealthCategory, days: Int = 30) -> [Double] {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        let categoryData = healthData.filter { 
            $0.category == category && $0.recordedAt >= cutoffDate 
        }.sorted { $0.recordedAt < $1.recordedAt }
        
        return categoryData.map { $0.value }
    }
    
    // MARK: - 健康目标管理
    
    // 添加健康目标
    func addHealthGoal(
        category: HealthCategory,
        title: String,
        description: String,
        targetValue: Double,
        unit: String,
        deadline: Date
    ) {
        let goal = HealthGoal(
            category: category,
            title: title,
            description: description,
            targetValue: targetValue,
            unit: unit,
            deadline: deadline
        )
        healthGoals.append(goal)
        saveHealthGoals()
    }
    
    // 更新目标进度
    func updateGoalProgress(_ goal: HealthGoal, currentValue: Double) {
        if let index = healthGoals.firstIndex(where: { $0.id == goal.id }) {
            let updatedGoal = HealthGoal(
                category: goal.category,
                title: goal.title,
                description: goal.description,
                targetValue: goal.targetValue,
                currentValue: currentValue,
                unit: goal.unit,
                deadline: goal.deadline
            )
            healthGoals[index] = updatedGoal
            saveHealthGoals()
        }
    }
    
    // 删除健康目标
    func deleteHealthGoal(_ goal: HealthGoal) {
        healthGoals.removeAll { $0.id == goal.id }
        saveHealthGoals()
    }
    
    // 获取活跃目标
    func getActiveGoals() -> [HealthGoal] {
        return healthGoals.filter { !$0.isCompleted && $0.deadline >= Date() }
    }
    
    // 获取已完成目标
    func getCompletedGoals() -> [HealthGoal] {
        return healthGoals.filter { $0.isCompleted }
    }
    
    // 获取过期目标
    func getExpiredGoals() -> [HealthGoal] {
        return healthGoals.filter { !$0.isCompleted && $0.deadline < Date() }
    }
    
    // MARK: - 健康提醒管理
    
    // 添加健康提醒
    func addHealthReminder(
        title: String,
        message: String,
        category: HealthCategory,
        scheduledTime: Date,
        isRepeating: Bool = false
    ) {
        let reminder = HealthReminder(
            title: title,
            message: message,
            category: category,
            scheduledTime: scheduledTime,
            isRepeating: isRepeating
        )
        healthReminders.append(reminder)
        saveHealthReminders()
    }
    
    // 完成提醒
    func completeReminder(_ reminder: HealthReminder) {
        if let index = healthReminders.firstIndex(where: { $0.id == reminder.id }) {
            let completedReminder = HealthReminder(
                title: reminder.title,
                message: reminder.message,
                category: reminder.category,
                scheduledTime: reminder.scheduledTime,
                isRepeating: reminder.isRepeating
            )
            healthReminders[index] = completedReminder
            saveHealthReminders()
        }
    }
    
    // 删除健康提醒
    func deleteHealthReminder(_ reminder: HealthReminder) {
        healthReminders.removeAll { $0.id == reminder.id }
        saveHealthReminders()
    }
    
    // 获取待处理提醒
    func getPendingReminders() -> [HealthReminder] {
        return healthReminders.filter { !$0.isCompleted && $0.scheduledTime <= Date() }
    }
    
    // 获取今日提醒
    func getTodayReminders() -> [HealthReminder] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today) ?? Date()
        
        return healthReminders.filter { 
            $0.scheduledTime >= today && $0.scheduledTime < tomorrow 
        }
    }
    
    // MARK: - 数据统计
    
    // 获取分类统计
    func getCategoryStatistics() -> [HealthCategory: Int] {
        var statistics: [HealthCategory: Int] = [:]
        
        for category in HealthCategory.allCases {
            statistics[category] = healthData.filter { $0.category == category }.count
        }
        
        return statistics
    }
    
    // 获取本周数据统计
    func getWeeklyStatistics() -> [String: Int] {
        let weekAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        let weeklyData = healthData.filter { $0.recordedAt >= weekAgo }
        
        var dailyCount: [String: Int] = [:]
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd"
        
        for entry in weeklyData {
            let dateString = formatter.string(from: entry.recordedAt)
            dailyCount[dateString, default: 0] += 1
        }
        
        return dailyCount
    }
    
    // 获取目标完成率
    func getGoalCompletionRate() -> Double {
        guard !healthGoals.isEmpty else { return 0.0 }
        let completedCount = healthGoals.filter { $0.isCompleted }.count
        return Double(completedCount) / Double(healthGoals.count)
    }
    
    // MARK: - 数据持久化
    
    private func loadHealthData() {
        if let data = UserDefaults.standard.data(forKey: healthDataKey) {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            healthData = (try? decoder.decode([HealthDataEntry].self, from: data)) ?? []
        }
    }
    
    private func saveHealthData() {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        if let data = try? encoder.encode(healthData) {
            UserDefaults.standard.set(data, forKey: healthDataKey)
        }
    }
    
    private func loadHealthGoals() {
        if let data = UserDefaults.standard.data(forKey: healthGoalsKey) {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            healthGoals = (try? decoder.decode([HealthGoal].self, from: data)) ?? []
        }
    }
    
    private func saveHealthGoals() {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        if let data = try? encoder.encode(healthGoals) {
            UserDefaults.standard.set(data, forKey: healthGoalsKey)
        }
    }
    
    private func loadHealthReminders() {
        if let data = UserDefaults.standard.data(forKey: healthRemindersKey) {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            healthReminders = (try? decoder.decode([HealthReminder].self, from: data)) ?? []
        }
    }
    
    private func saveHealthReminders() {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        if let data = try? encoder.encode(healthReminders) {
            UserDefaults.standard.set(data, forKey: healthRemindersKey)
        }
    }
    
    // 清空所有数据
    func clearAllData() {
        healthData.removeAll()
        healthGoals.removeAll()
        healthReminders.removeAll()
        
        UserDefaults.standard.removeObject(forKey: healthDataKey)
        UserDefaults.standard.removeObject(forKey: healthGoalsKey)
        UserDefaults.standard.removeObject(forKey: healthRemindersKey)
    }
}
