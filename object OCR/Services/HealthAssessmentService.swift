//
//  HealthAssessmentService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation

class HealthAssessmentService: ObservableObject {
    @Published var assessmentHistory: [HealthAssessment] = []
    
    private let assessmentHistoryKey = "assessment_history"
    
    init() {
        loadAssessmentHistory()
    }
    
    // 生成健康评估报告
    func generateAssessment(from questionService: HealthQuestionService) -> HealthAssessment {
        let totalScore = questionService.calculateTotalScore()
        let categoryScores = questionService.calculateCategoryScores()
        let riskLevel = determineRiskLevel(totalScore: totalScore)
        let overallRiskLevel = determineOverallRiskLevel(categoryScores: categoryScores)
        let recommendations = generateRecommendations(
            totalScore: totalScore,
            categoryScores: categoryScores,
            answers: questionService.userAnswers,
            questions: questionService.questions
        )
        
        let assessment = HealthAssessment(
            userId: nil,
            answers: questionService.userAnswers,
            totalScore: totalScore,
            categoryScores: categoryScores,
            recommendations: recommendations,
            riskLevel: riskLevel,
            overallRiskLevel: overallRiskLevel
        )
        
        // 保存到历史记录
        assessmentHistory.insert(assessment, at: 0)
        saveAssessmentHistory()
        
        return assessment
    }
    
    // 确定风险等级
    private func determineRiskLevel(totalScore: Int) -> RiskLevel {
        // 基于总分确定风险等级
        // 假设满分为100分（20个问题，每个最高5分）
        let percentage = Double(totalScore) / 100.0
        
        switch percentage {
        case 0.8...1.0:
            return .low
        case 0.6..<0.8:
            return .moderate
        case 0.4..<0.6:
            return .high
        default:
            return .critical
        }
    }
    
    // 确定整体风险等级
    private func determineOverallRiskLevel(categoryScores: [HealthCategory: Int]) -> RiskLevel {
        let scores = Array(categoryScores.values)
        let averageScore = scores.isEmpty ? 0 : scores.reduce(0, +) / scores.count
        
        // 每个分类最多有2-3个问题，每个问题最高5分，所以平均分大约在10-15分之间
        switch averageScore {
        case 12...15:
            return .low
        case 8..<12:
            return .moderate
        case 4..<8:
            return .high
        default:
            return .critical
        }
    }
    
    // 生成个性化建议
    private func generateRecommendations(
        totalScore: Int,
        categoryScores: [HealthCategory: Int],
        answers: [UserAnswer],
        questions: [HealthQuestion]
    ) -> [String] {
        var recommendations: [String] = []
        
        // 基于分类得分生成建议
        let sortedCategories = categoryScores.sorted { $0.value < $1.value }
        
        for (category, score) in sortedCategories.prefix(3) {
            if score < 8 { // 得分较低的分类
                switch category {
                case .physical:
                    recommendations.append("建议增加体育锻炼，保持规律作息，定期进行健康检查")
                case .mental:
                    recommendations.append("建议学习压力管理技巧，保持积极心态，必要时寻求专业心理帮助")
                case .lifestyle:
                    recommendations.append("建议改善生活习惯，戒烟限酒，减少久坐时间")
                case .nutrition:
                    recommendations.append("建议均衡饮食，多吃蔬菜水果，控制高热量食物摄入")
                case .sleep:
                    recommendations.append("建议建立规律的睡眠习惯，创造良好的睡眠环境")
                case .exercise:
                    recommendations.append("建议制定运动计划，每周至少150分钟中等强度运动")
                case .work:
                    recommendations.append("建议合理安排工作时间，学会工作与生活的平衡")
                case .social:
                    recommendations.append("建议多参与社交活动，维护良好的人际关系")
                case .environment:
                    recommendations.append("建议改善居住和工作环境，注意空气质量和噪音控制")
                case .preventive:
                    recommendations.append("建议定期体检，及时接种疫苗，关注预防保健")
                }
            }
        }
        
        // 基于总分生成通用建议
        let riskLevel = determineRiskLevel(totalScore: totalScore)
        switch riskLevel {
        case .low:
            recommendations.append("您的健康状况良好，请继续保持健康的生活方式")
        case .moderate:
            recommendations.append("您的健康状况尚可，建议在某些方面进行改善")
        case .high:
            recommendations.append("您的健康状况需要关注，建议及时调整生活方式")
        case .critical:
            recommendations.append("您的健康状况令人担忧，强烈建议咨询医疗专业人员")
        }
        
        // 基于具体答案生成针对性建议
        for answer in answers {
            if let question = questions.first(where: { $0.id == answer.questionId }) {
                // 针对特定问题的低分答案给出建议
                if question.type == .scale, let scaleValue = answer.scaleValue, scaleValue <= 3 {
                    if question.category == .mental && question.title.contains("压力") {
                        recommendations.append("建议学习放松技巧，如深呼吸、冥想或瑜伽")
                    }
                }
                
                // 针对选择题的低分选项给出建议
                for optionId in answer.selectedOptionIds {
                    if let option = question.options.first(where: { $0.id == optionId }), option.score <= 2 {
                        if question.title.contains("吸烟") && option.text.contains("吸烟") {
                            recommendations.append("吸烟严重危害健康，建议寻求专业戒烟帮助")
                        }
                        if question.title.contains("饮酒") && option.text.contains("大量") {
                            recommendations.append("过量饮酒对健康有害，建议逐步减少饮酒量")
                        }
                        if question.title.contains("睡眠时间") && option.text.contains("少于") {
                            recommendations.append("睡眠不足会影响身心健康，建议保证每天7-9小时睡眠")
                        }
                    }
                }
            }
        }
        
        // 去重并限制建议数量
        let uniqueRecommendations = Array(Set(recommendations))
        return Array(uniqueRecommendations.prefix(8))
    }
    
    // 加载评估历史
    private func loadAssessmentHistory() {
        if let data = UserDefaults.standard.data(forKey: assessmentHistoryKey) {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            assessmentHistory = (try? decoder.decode([HealthAssessment].self, from: data)) ?? []
        }
    }
    
    // 保存评估历史
    private func saveAssessmentHistory() {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        if let data = try? encoder.encode(assessmentHistory) {
            UserDefaults.standard.set(data, forKey: assessmentHistoryKey)
        }
    }
    
    // 删除评估记录
    func deleteAssessment(_ assessment: HealthAssessment) {
        assessmentHistory.removeAll { $0.id == assessment.id }
        saveAssessmentHistory()
    }
    
    // 清空历史记录
    func clearHistory() {
        assessmentHistory.removeAll()
        saveAssessmentHistory()
    }
    
    // 获取最新评估
    var latestAssessment: HealthAssessment? {
        return assessmentHistory.first
    }
    
    // 获取评估趋势
    func getAssessmentTrend() -> [Int] {
        return assessmentHistory.reversed().map { $0.totalScore }
    }
    
    // 获取分类趋势
    func getCategoryTrend(for category: HealthCategory) -> [Int] {
        return assessmentHistory.reversed().compactMap { $0.categoryScores[category] }
    }
}
