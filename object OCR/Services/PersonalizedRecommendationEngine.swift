//
//  PersonalizedRecommendationEngine.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation
import SwiftUI

/// 个性化推荐引擎 - 基于用户健康状况生成个性化建议
class PersonalizedRecommendationEngine: ObservableObject {
    
    @Published var currentRecommendations: [PersonalizedRecommendation] = []
    @Published var isGenerating = false
    
    // MARK: - 推荐数据结构
    
    struct PersonalizedRecommendation: Identifiable, Codable {
        let id = UUID()
        let category: RecommendationCategory
        let title: String
        let description: String
        let priority: Priority
        let difficulty: Difficulty
        let estimatedTime: String
        let expectedBenefit: String
        let actionSteps: [ActionStep]
        let trackingMetrics: [String]
        let personalizedReason: String
        let scientificBasis: String
        let createdDate: Date
        
        enum RecommendationCategory: String, CaseIterable, Codable {
            case nutrition = "营养"
            case exercise = "运动"
            case sleep = "睡眠"
            case stress = "压力管理"
            case medical = "医疗"
            case lifestyle = "生活方式"
            case mental = "心理健康"
            
            var icon: String {
                switch self {
                case .nutrition: return "leaf.fill"
                case .exercise: return "figure.run"
                case .sleep: return "bed.double.fill"
                case .stress: return "brain.head.profile"
                case .medical: return "stethoscope"
                case .lifestyle: return "house.fill"
                case .mental: return "heart.fill"
                }
            }
            
            var color: Color {
                switch self {
                case .nutrition: return .green
                case .exercise: return .blue
                case .sleep: return .purple
                case .stress: return .orange
                case .medical: return .red
                case .lifestyle: return .teal
                case .mental: return .pink
                }
            }
        }
        
        enum Priority: String, CaseIterable, Codable {
            case urgent = "紧急"
            case high = "高"
            case medium = "中"
            case low = "低"
            
            var color: Color {
                switch self {
                case .urgent: return .red
                case .high: return .orange
                case .medium: return .yellow
                case .low: return .green
                }
            }
        }
        
        enum Difficulty: String, CaseIterable, Codable {
            case easy = "简单"
            case moderate = "中等"
            case hard = "困难"
            case expert = "专家级"
            
            var color: Color {
                switch self {
                case .easy: return .green
                case .moderate: return .yellow
                case .hard: return .orange
                case .expert: return .red
                }
            }
        }
    }
    
    struct ActionStep: Identifiable, Codable {
        let id = UUID()
        let stepNumber: Int
        let title: String
        let description: String
        let duration: String
        let frequency: String
        let tips: [String]
        let isCompleted: Bool
    }
    
    // MARK: - 主要推荐生成方法
    
    /// 生成综合个性化推荐
    func generatePersonalizedRecommendations(
        profile: ExtendedUserProfile,
        healthAnalysis: HealthAnalysisResult,
        trendAnalysis: [HealthTrendAnalysisService.TrendAnalysisResult]
    ) async -> [PersonalizedRecommendation] {
        
        isGenerating = true
        defer { isGenerating = false }
        
        var recommendations: [PersonalizedRecommendation] = []
        
        // 基于风险评估的推荐
        recommendations.append(contentsOf: generateRiskBasedRecommendations(
            profile: profile,
            riskAssessment: healthAnalysis.riskAssessment
        ))
        
        // 基于生活方式分析的推荐
        recommendations.append(contentsOf: generateLifestyleRecommendations(
            profile: profile,
            lifestyleAnalysis: healthAnalysis.lifestyleAnalysis
        ))
        
        // 基于营养分析的推荐
        recommendations.append(contentsOf: generateNutritionRecommendations(
            profile: profile,
            nutritionAnalysis: healthAnalysis.nutritionAnalysis
        ))
        
        // 基于运动分析的推荐
        recommendations.append(contentsOf: generateFitnessRecommendations(
            profile: profile,
            fitnessAnalysis: healthAnalysis.fitnessAnalysis
        ))
        
        // 基于心理健康评估的推荐
        recommendations.append(contentsOf: generateMentalHealthRecommendations(
            profile: profile,
            mentalHealthScore: healthAnalysis.mentalHealthScore
        ))
        
        // 基于趋势分析的推荐
        recommendations.append(contentsOf: generateTrendBasedRecommendations(
            profile: profile,
            trends: trendAnalysis
        ))
        
        // 基于个人特征的推荐
        recommendations.append(contentsOf: generatePersonalizedLifestyleRecommendations(
            profile: profile
        ))
        
        // 排序和筛选推荐
        let sortedRecommendations = prioritizeRecommendations(recommendations)
        let finalRecommendations = Array(sortedRecommendations.prefix(10)) // 限制推荐数量
        
        await MainActor.run {
            self.currentRecommendations = finalRecommendations
        }
        
        return finalRecommendations
    }
    
    // MARK: - 基于风险评估的推荐
    
    private func generateRiskBasedRecommendations(
        profile: ExtendedUserProfile,
        riskAssessment: RiskAssessment
    ) -> [PersonalizedRecommendation] {
        
        var recommendations: [PersonalizedRecommendation] = []
        
        for risk in riskAssessment.specificRisks {
            switch risk.condition {
            case "心血管疾病":
                recommendations.append(contentsOf: generateCardiovascularRecommendations(profile: profile, risk: risk))
            case "2型糖尿病":
                recommendations.append(contentsOf: generateDiabetesRecommendations(profile: profile, risk: risk))
            case "癌症":
                recommendations.append(contentsOf: generateCancerPreventionRecommendations(profile: profile, risk: risk))
            case "心理健康问题":
                recommendations.append(contentsOf: generateMentalHealthRiskRecommendations(profile: profile, risk: risk))
            default:
                break
            }
        }
        
        return recommendations
    }
    
    private func generateCardiovascularRecommendations(
        profile: ExtendedUserProfile,
        risk: HealthRisk
    ) -> [PersonalizedRecommendation] {
        
        var recommendations: [PersonalizedRecommendation] = []
        
        // DASH饮食推荐
        if profile.nutrition.dietaryHabits.sodiumIntake == .high {
            recommendations.append(PersonalizedRecommendation(
                category: .nutrition,
                title: "采用DASH饮食模式",
                description: "降低钠盐摄入，增加钾、镁、钙的摄入，有效降低血压",
                priority: .high,
                difficulty: .moderate,
                estimatedTime: "2-4周见效",
                expectedBenefit: "可降低收缩压8-14mmHg",
                actionSteps: generateDASHDietSteps(),
                trackingMetrics: ["血压", "钠盐摄入量", "蔬菜水果摄入"],
                personalizedReason: "您的钠盐摄入量较高，是心血管疾病的重要风险因素",
                scientificBasis: "DASH饮食已被证实能有效降低血压和心血管疾病风险",
                createdDate: Date()
            ))
        }
        
        // 有氧运动推荐
        if profile.physicalActivity.exerciseFrequency == .rarely || profile.physicalActivity.exerciseFrequency == .never {
            recommendations.append(PersonalizedRecommendation(
                category: .exercise,
                title: "规律有氧运动计划",
                description: "每周进行150分钟中等强度有氧运动，改善心血管健康",
                priority: .high,
                difficulty: .easy,
                estimatedTime: "4-6周见效",
                expectedBenefit: "降低心血管疾病风险30-35%",
                actionSteps: generateCardioExerciseSteps(profile: profile),
                trackingMetrics: ["运动时长", "心率", "血压"],
                personalizedReason: "您目前运动量不足，增加有氧运动对心血管健康极为重要",
                scientificBasis: "规律有氧运动能改善心肺功能，降低血压和胆固醇",
                createdDate: Date()
            ))
        }
        
        return recommendations
    }
    
    private func generateDiabetesRecommendations(
        profile: ExtendedUserProfile,
        risk: HealthRisk
    ) -> [PersonalizedRecommendation] {
        
        var recommendations: [PersonalizedRecommendation] = []
        
        // 低GI饮食推荐
        recommendations.append(PersonalizedRecommendation(
            category: .nutrition,
            title: "低血糖指数饮食",
            description: "选择低GI食物，控制血糖波动，预防糖尿病",
            priority: .high,
            difficulty: .moderate,
            estimatedTime: "2-3周适应",
            expectedBenefit: "降低糖尿病风险40-60%",
            actionSteps: generateLowGIDietSteps(),
            trackingMetrics: ["血糖水平", "体重", "腰围"],
            personalizedReason: "基于您的血糖水平和家族史，低GI饮食能有效预防糖尿病",
            scientificBasis: "低GI饮食能改善胰岛素敏感性，控制血糖",
            createdDate: Date()
        ))
        
        // 体重管理推荐
        if profile.basicInfo.bmi > 25 {
            recommendations.append(PersonalizedRecommendation(
                category: .lifestyle,
                title: "渐进式体重管理",
                description: "通过合理饮食和运动，每月减重1-2公斤",
                priority: .high,
                difficulty: .moderate,
                estimatedTime: "3-6个月",
                expectedBenefit: "减重5-10%可降低糖尿病风险58%",
                actionSteps: generateWeightManagementSteps(profile: profile),
                trackingMetrics: ["体重", "BMI", "腰围", "体脂率"],
                personalizedReason: "您的BMI为\(String(format: "%.1f", profile.basicInfo.bmi))，减重对预防糖尿病很重要",
                scientificBasis: "适度减重能显著改善胰岛素敏感性",
                createdDate: Date()
            ))
        }
        
        return recommendations
    }
    
    // MARK: - 基于生活方式分析的推荐
    
    private func generateLifestyleRecommendations(
        profile: ExtendedUserProfile,
        lifestyleAnalysis: LifestyleAnalysis
    ) -> [PersonalizedRecommendation] {
        
        var recommendations: [PersonalizedRecommendation] = []
        
        // 睡眠优化推荐
        if profile.lifestyle.sleepPattern.averageHoursPerNight < 7 {
            recommendations.append(PersonalizedRecommendation(
                category: .sleep,
                title: "睡眠质量优化计划",
                description: "建立规律作息，提高睡眠质量，每晚保证7-9小时睡眠",
                priority: .medium,
                difficulty: .easy,
                estimatedTime: "2-3周建立习惯",
                expectedBenefit: "改善免疫力，降低慢性病风险",
                actionSteps: generateSleepOptimizationSteps(),
                trackingMetrics: ["睡眠时长", "睡眠质量", "入睡时间"],
                personalizedReason: "您的平均睡眠时间为\(String(format: "%.1f", profile.lifestyle.sleepPattern.averageHoursPerNight))小时，不足推荐的7-9小时",
                scientificBasis: "充足睡眠对维持免疫功能和代谢健康至关重要",
                createdDate: Date()
            ))
        }
        
        // 压力管理推荐
        if profile.mentalHealth.stressFactors.contains(where: { $0.intensity == .high || $0.intensity == .severe }) {
            recommendations.append(PersonalizedRecommendation(
                category: .stress,
                title: "正念减压训练",
                description: "学习正念冥想和深呼吸技巧，有效管理日常压力",
                priority: .medium,
                difficulty: .easy,
                estimatedTime: "每天10-20分钟",
                expectedBenefit: "降低压力激素水平，改善心理健康",
                actionSteps: generateStressManagementSteps(),
                trackingMetrics: ["压力水平", "心率变异性", "情绪状态"],
                personalizedReason: "您目前面临较高的压力水平，需要有效的压力管理策略",
                scientificBasis: "正念练习已被证实能降低皮质醇水平，改善心理健康",
                createdDate: Date()
            ))
        }
        
        return recommendations
    }
    
    // MARK: - 基于营养分析的推荐
    
    private func generateNutritionRecommendations(
        profile: ExtendedUserProfile,
        nutritionAnalysis: NutritionAnalysis
    ) -> [PersonalizedRecommendation] {
        
        var recommendations: [PersonalizedRecommendation] = []
        
        // 蛋白质摄入优化
        if nutritionAnalysis.proteinIntake < 0.8 * profile.basicInfo.weight {
            recommendations.append(PersonalizedRecommendation(
                category: .nutrition,
                title: "优质蛋白质补充计划",
                description: "增加优质蛋白质摄入，维持肌肉量和代谢健康",
                priority: .medium,
                difficulty: .easy,
                estimatedTime: "1-2周适应",
                expectedBenefit: "改善肌肉量，提高代谢率",
                actionSteps: generateProteinSupplementSteps(profile: profile),
                trackingMetrics: ["蛋白质摄入量", "肌肉量", "体重"],
                personalizedReason: "您的蛋白质摄入量不足，建议每公斤体重摄入0.8-1.2克蛋白质",
                scientificBasis: "充足的蛋白质摄入对维持肌肉量和代谢健康至关重要",
                createdDate: Date()
            ))
        }
        
        // 微量营养素补充
        if profile.nutrition.dietaryHabits.vegetableIntake == .low {
            recommendations.append(PersonalizedRecommendation(
                category: .nutrition,
                title: "彩虹饮食计划",
                description: "每天摄入5种不同颜色的蔬菜水果，补充多种维生素和矿物质",
                priority: .medium,
                difficulty: .easy,
                estimatedTime: "持续执行",
                expectedBenefit: "提高抗氧化能力，降低慢性病风险",
                actionSteps: generateRainbowDietSteps(),
                trackingMetrics: ["蔬菜水果摄入量", "维生素水平"],
                personalizedReason: "您的蔬菜摄入量较低，需要增加多样化的植物性食物",
                scientificBasis: "多样化的蔬菜水果能提供丰富的抗氧化物质和纤维",
                createdDate: Date()
            ))
        }
        
        return recommendations
    }
    
    // MARK: - 基于运动分析的推荐
    
    private func generateFitnessRecommendations(
        profile: ExtendedUserProfile,
        fitnessAnalysis: FitnessAnalysis
    ) -> [PersonalizedRecommendation] {
        
        var recommendations: [PersonalizedRecommendation] = []
        
        // 力量训练推荐
        if profile.physicalActivity.strengthTraining == .never {
            recommendations.append(PersonalizedRecommendation(
                category: .exercise,
                title: "基础力量训练计划",
                description: "每周2-3次力量训练，提高肌肉量和骨密度",
                priority: .medium,
                difficulty: .moderate,
                estimatedTime: "每次30-45分钟",
                expectedBenefit: "增加肌肉量，提高基础代谢率",
                actionSteps: generateStrengthTrainingSteps(profile: profile),
                trackingMetrics: ["肌肉量", "力量水平", "骨密度"],
                personalizedReason: "您目前缺乏力量训练，这对维持肌肉量和骨健康很重要",
                scientificBasis: "力量训练能有效预防肌肉流失和骨质疏松",
                createdDate: Date()
            ))
        }
        
        // 柔韧性训练推荐
        recommendations.append(PersonalizedRecommendation(
            category: .exercise,
            title: "柔韧性和平衡训练",
            description: "每天进行拉伸和平衡练习，提高身体灵活性",
            priority: .low,
            difficulty: .easy,
            estimatedTime: "每天10-15分钟",
            expectedBenefit: "改善关节活动度，预防跌倒",
            actionSteps: generateFlexibilityTrainingSteps(),
            trackingMetrics: ["关节活动度", "平衡能力"],
            personalizedReason: "柔韧性训练对所有年龄段都很重要，特别是预防运动损伤",
            scientificBasis: "规律的拉伸能改善关节活动度和肌肉功能",
            createdDate: Date()
        ))
        
        return recommendations
    }
    
    // MARK: - 基于心理健康评估的推荐
    
    private func generateMentalHealthRecommendations(
        profile: ExtendedUserProfile,
        mentalHealthScore: Double
    ) -> [PersonalizedRecommendation] {
        
        var recommendations: [PersonalizedRecommendation] = []
        
        if mentalHealthScore < 70 {
            recommendations.append(PersonalizedRecommendation(
                category: .mental,
                title: "心理健康提升计划",
                description: "通过多种方式改善心理健康状态，提高生活质量",
                priority: .high,
                difficulty: .moderate,
                estimatedTime: "4-8周见效",
                expectedBenefit: "改善情绪状态，提高生活满意度",
                actionSteps: generateMentalHealthImprovementSteps(),
                trackingMetrics: ["情绪状态", "压力水平", "生活满意度"],
                personalizedReason: "您的心理健康评分为\(String(format: "%.1f", mentalHealthScore))，需要关注心理健康",
                scientificBasis: "综合的心理健康干预能有效改善情绪和认知功能",
                createdDate: Date()
            ))
        }
        
        return recommendations
    }

    // MARK: - 基于趋势分析的推荐

    private func generateTrendBasedRecommendations(
        profile: ExtendedUserProfile,
        trends: [HealthTrendAnalysisService.TrendAnalysisResult]
    ) -> [PersonalizedRecommendation] {

        var recommendations: [PersonalizedRecommendation] = []

        for trend in trends {
            if trend.trend == .declining && trend.significance == .high {
                switch trend.metric {
                case "体重":
                    if trend.changeRate > 0 { // 体重增加
                        recommendations.append(PersonalizedRecommendation(
                            category: .lifestyle,
                            title: "体重控制紧急计划",
                            description: "您的体重呈上升趋势，需要立即采取行动控制体重",
                            priority: .urgent,
                            difficulty: .moderate,
                            estimatedTime: "4-6周见效",
                            expectedBenefit: "阻止体重继续上升，恢复健康体重",
                            actionSteps: generateEmergencyWeightControlSteps(),
                            trackingMetrics: ["体重", "腰围", "体脂率"],
                            personalizedReason: "您的体重在\(trend.timeframe)内上升了\(String(format: "%.1f", abs(trend.changeRate)))%",
                            scientificBasis: "及时的体重干预能有效预防肥胖相关疾病",
                            createdDate: Date()
                        ))
                    }
                case "血压":
                    recommendations.append(PersonalizedRecommendation(
                        category: .medical,
                        title: "血压管理强化方案",
                        description: "您的血压呈上升趋势，需要加强血压管理",
                        priority: .urgent,
                        difficulty: .moderate,
                        estimatedTime: "2-4周见效",
                        expectedBenefit: "控制血压上升，降低心血管风险",
                        actionSteps: generateBloodPressureControlSteps(),
                        trackingMetrics: ["血压", "心率", "钠盐摄入"],
                        personalizedReason: "您的血压在\(trend.timeframe)内呈上升趋势",
                        scientificBasis: "早期血压干预能有效预防心血管并发症",
                        createdDate: Date()
                    ))
                default:
                    break
                }
            }
        }

        return recommendations
    }

    // MARK: - 个性化生活方式推荐

    private func generatePersonalizedLifestyleRecommendations(
        profile: ExtendedUserProfile
    ) -> [PersonalizedRecommendation] {

        var recommendations: [PersonalizedRecommendation] = []

        // 基于年龄的推荐
        if profile.basicInfo.age > 50 {
            recommendations.append(PersonalizedRecommendation(
                category: .medical,
                title: "中老年健康筛查计划",
                description: "定期进行适合您年龄段的健康检查",
                priority: .medium,
                difficulty: .easy,
                estimatedTime: "每年1-2次",
                expectedBenefit: "早期发现和预防年龄相关疾病",
                actionSteps: generateAgeSpecificScreeningSteps(age: profile.basicInfo.age),
                trackingMetrics: ["检查结果", "健康指标"],
                personalizedReason: "您已\(profile.basicInfo.age)岁，需要加强健康监测",
                scientificBasis: "定期筛查能有效预防和早期发现慢性疾病",
                createdDate: Date()
            ))
        }

        // 基于性别的推荐
        if profile.basicInfo.gender == .female && profile.basicInfo.age > 40 {
            recommendations.append(PersonalizedRecommendation(
                category: .medical,
                title: "女性健康关爱计划",
                description: "关注女性特有的健康需求，包括骨健康和激素变化",
                priority: .medium,
                difficulty: .easy,
                estimatedTime: "持续关注",
                expectedBenefit: "维护女性特有的健康需求",
                actionSteps: generateWomenHealthSteps(age: profile.basicInfo.age),
                trackingMetrics: ["骨密度", "激素水平", "妇科检查"],
                personalizedReason: "作为\(profile.basicInfo.age)岁女性，需要特别关注骨健康和激素变化",
                scientificBasis: "女性在不同年龄段有特殊的健康需求",
                createdDate: Date()
            ))
        }

        return recommendations
    }

    // MARK: - 推荐优先级排序

    private func prioritizeRecommendations(_ recommendations: [PersonalizedRecommendation]) -> [PersonalizedRecommendation] {
        return recommendations.sorted { rec1, rec2 in
            // 首先按优先级排序
            let priority1 = priorityValue(rec1.priority)
            let priority2 = priorityValue(rec2.priority)

            if priority1 != priority2 {
                return priority1 > priority2
            }

            // 然后按难度排序（简单的优先）
            let difficulty1 = difficultyValue(rec1.difficulty)
            let difficulty2 = difficultyValue(rec2.difficulty)

            return difficulty1 < difficulty2
        }
    }

    private func priorityValue(_ priority: PersonalizedRecommendation.Priority) -> Int {
        switch priority {
        case .urgent: return 4
        case .high: return 3
        case .medium: return 2
        case .low: return 1
        }
    }

    private func difficultyValue(_ difficulty: PersonalizedRecommendation.Difficulty) -> Int {
        switch difficulty {
        case .easy: return 1
        case .moderate: return 2
        case .hard: return 3
        case .expert: return 4
        }
    }

    // MARK: - 行动步骤生成方法

    private func generateDASHDietSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "减少钠盐摄入", description: "每日钠摄入量控制在2300mg以下", duration: "持续", frequency: "每天", tips: ["使用香料代替盐调味", "选择低钠食品"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "增加蔬菜水果", description: "每天至少5份蔬菜水果", duration: "持续", frequency: "每天", tips: ["每餐包含蔬菜", "用水果作为零食"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "选择全谷物", description: "用全谷物替代精制谷物", duration: "持续", frequency: "每天", tips: ["选择糙米、全麦面包", "增加燕麦摄入"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "适量坚果豆类", description: "每周4-5次坚果或豆类", duration: "持续", frequency: "每周4-5次", tips: ["选择无盐坚果", "用豆类替代部分肉类"], isCompleted: false)
        ]
    }

    private func generateCardioExerciseSteps(profile: ExtendedUserProfile) -> [ActionStep] {
        let startingIntensity = profile.physicalActivity.exerciseFrequency == .never ? "低强度" : "中等强度"

        return [
            ActionStep(stepNumber: 1, title: "制定运动计划", description: "每周安排3-5次有氧运动", duration: "30分钟", frequency: "每周3-5次", tips: ["选择喜欢的运动", "循序渐进"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "开始\(startingIntensity)运动", description: "从快走或慢跑开始", duration: "20-30分钟", frequency: "每周3次", tips: ["保持能说话的强度", "注意心率监测"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "逐步增加强度", description: "每周增加5-10%的运动量", duration: "30-45分钟", frequency: "每周4-5次", tips: ["倾听身体信号", "适当休息"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "维持规律运动", description: "建立长期运动习惯", duration: "45分钟", frequency: "每周5次", tips: ["记录运动日志", "寻找运动伙伴"], isCompleted: false)
        ]
    }

    private func generateLowGIDietSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "学习GI知识", description: "了解食物的血糖指数", duration: "1周", frequency: "一次性", tips: ["使用GI食物表", "下载相关APP"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "替换高GI食物", description: "用低GI食物替代高GI食物", duration: "持续", frequency: "每餐", tips: ["糙米替代白米", "全麦面包替代白面包"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "合理搭配食物", description: "蛋白质和纤维与碳水化合物搭配", duration: "持续", frequency: "每餐", tips: ["先吃蔬菜再吃主食", "加入适量蛋白质"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "监测血糖反应", description: "观察餐后血糖变化", duration: "持续", frequency: "每天", tips: ["记录血糖日志", "调整食物搭配"], isCompleted: false)
        ]
    }

    private func generateWeightManagementSteps(profile: ExtendedUserProfile) -> [ActionStep] {
        let targetWeightLoss = (profile.basicInfo.bmi - 24) * profile.basicInfo.weight / profile.basicInfo.bmi

        return [
            ActionStep(stepNumber: 1, title: "设定减重目标", description: "目标减重\(String(format: "%.1f", targetWeightLoss))公斤", duration: "3-6个月", frequency: "一次性", tips: ["设定现实目标", "分阶段实施"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "控制热量摄入", description: "每日减少300-500卡路里", duration: "持续", frequency: "每天", tips: ["记录饮食日志", "控制份量"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "增加体力活动", description: "每天增加30分钟运动", duration: "30分钟", frequency: "每天", tips: ["选择喜欢的运动", "增加日常活动"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "定期监测进展", description: "每周测量体重和腰围", duration: "5分钟", frequency: "每周", tips: ["固定时间测量", "记录变化趋势"], isCompleted: false)
        ]
    }

    private func generateSleepOptimizationSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "建立睡眠时间表", description: "固定就寝和起床时间", duration: "持续", frequency: "每天", tips: ["包括周末也要坚持", "逐步调整时间"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "创造睡眠环境", description: "保持卧室安静、黑暗、凉爽", duration: "持续", frequency: "每天", tips: ["使用遮光窗帘", "控制室温18-22度"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "睡前放松仪式", description: "睡前1小时进行放松活动", duration: "1小时", frequency: "每天", tips: ["避免电子设备", "可以阅读或冥想"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "监测睡眠质量", description: "记录睡眠时间和质量", duration: "持续", frequency: "每天", tips: ["使用睡眠APP", "注意睡眠模式"], isCompleted: false)
        ]
    }

    private func generateStressManagementSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "学习深呼吸技巧", description: "掌握4-7-8呼吸法", duration: "5-10分钟", frequency: "每天2-3次", tips: ["吸气4秒，屏气7秒，呼气8秒", "在安静环境练习"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "开始正念冥想", description: "每天进行正念冥想练习", duration: "10-20分钟", frequency: "每天", tips: ["使用冥想APP指导", "从短时间开始"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "识别压力源", description: "记录和分析压力触发因素", duration: "持续", frequency: "每天", tips: ["写压力日志", "寻找应对策略"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "建立支持网络", description: "与家人朋友分享感受", duration: "持续", frequency: "定期", tips: ["主动寻求帮助", "参加支持小组"], isCompleted: false)
        ]
    }

    private func generateProteinSupplementSteps(profile: ExtendedUserProfile) -> [ActionStep] {
        let targetProtein = profile.basicInfo.weight * 1.0 // 每公斤体重1克蛋白质

        return [
            ActionStep(stepNumber: 1, title: "计算蛋白质需求", description: "目标每日摄入\(String(format: "%.0f", targetProtein))克蛋白质", duration: "持续", frequency: "每天", tips: ["分配到三餐中", "选择优质蛋白"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "增加蛋白质食物", description: "每餐包含蛋白质食物", duration: "持续", frequency: "每餐", tips: ["瘦肉、鱼类、蛋类", "豆类、坚果"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "合理分配时间", description: "运动后30分钟内补充蛋白质", duration: "30分钟内", frequency: "运动后", tips: ["选择易消化蛋白", "搭配少量碳水"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "监测摄入量", description: "记录每日蛋白质摄入", duration: "持续", frequency: "每天", tips: ["使用营养APP", "调整食物搭配"], isCompleted: false)
        ]
    }

    private func generateRainbowDietSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "了解彩虹饮食", description: "学习不同颜色食物的营养价值", duration: "1周", frequency: "一次性", tips: ["红色：番茄、胡萝卜", "绿色：菠菜、西兰花"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "每日5种颜色", description: "每天摄入5种不同颜色的蔬果", duration: "持续", frequency: "每天", tips: ["制作颜色搭配表", "拍照记录"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "增加摄入量", description: "每天至少400克蔬菜水果", duration: "持续", frequency: "每天", tips: ["每餐包含蔬菜", "用水果作零食"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "尝试新品种", description: "每周尝试1-2种新的蔬果", duration: "持续", frequency: "每周", tips: ["季节性选择", "多样化烹饪"], isCompleted: false)
        ]
    }

    private func generateStrengthTrainingSteps(profile: ExtendedUserProfile) -> [ActionStep] {
        let startingLevel = profile.basicInfo.age > 50 ? "初级" : "基础"

        return [
            ActionStep(stepNumber: 1, title: "学习基础动作", description: "掌握\(startingLevel)力量训练动作", duration: "1-2周", frequency: "学习阶段", tips: ["深蹲、俯卧撑、平板支撑", "注重动作标准"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "制定训练计划", description: "每周2-3次全身力量训练", duration: "30-45分钟", frequency: "每周2-3次", tips: ["大肌群优先", "充分休息"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "逐步增加强度", description: "每2周增加重量或次数", duration: "持续", frequency: "每2周调整", tips: ["循序渐进", "避免过度训练"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "记录训练进展", description: "记录重量、次数、感受", duration: "持续", frequency: "每次训练", tips: ["使用训练日志", "定期评估进展"], isCompleted: false)
        ]
    }

    private func generateFlexibilityTrainingSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "晨起拉伸", description: "每天早上进行全身拉伸", duration: "10分钟", frequency: "每天", tips: ["温和缓慢", "避免弹震式拉伸"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "运动后拉伸", description: "运动后进行目标肌群拉伸", duration: "10-15分钟", frequency: "每次运动后", tips: ["保持15-30秒", "感受肌肉放松"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "平衡训练", description: "练习单脚站立等平衡动作", duration: "5-10分钟", frequency: "每天", tips: ["从简单开始", "可借助支撑"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "瑜伽或太极", description: "参加瑜伽或太极课程", duration: "45-60分钟", frequency: "每周1-2次", tips: ["选择适合的课程", "注重呼吸配合"], isCompleted: false)
        ]
    }

    private func generateMentalHealthImprovementSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "情绪识别", description: "学会识别和命名自己的情绪", duration: "持续", frequency: "每天", tips: ["写情绪日记", "使用情绪词汇表"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "积极活动", description: "每天进行一项愉快的活动", duration: "30分钟", frequency: "每天", tips: ["听音乐、画画、园艺", "与朋友交流"], isCompleted: false),
            ActionStep(stepNumber: 3, title: "认知重构", description: "挑战负面思维模式", duration: "持续", frequency: "需要时", tips: ["质疑负面想法", "寻找证据和替代想法"], isCompleted: false),
            ActionStep(stepNumber: 4, title: "寻求支持", description: "必要时寻求专业心理帮助", duration: "持续", frequency: "需要时", tips: ["不要害怕求助", "选择合适的专业人士"], isCompleted: false)
        ]
    }

    // 其他辅助方法...
    private func generateEmergencyWeightControlSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "立即调整饮食", description: "减少高热量食物摄入", duration: "立即开始", frequency: "每天", tips: ["避免油炸食品", "控制份量"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "增加运动频率", description: "每天至少30分钟有氧运动", duration: "30分钟", frequency: "每天", tips: ["快走、游泳、骑车", "分次进行也可以"], isCompleted: false)
        ]
    }

    private func generateBloodPressureControlSteps() -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "严格限盐", description: "每日钠摄入量低于1500mg", duration: "立即开始", frequency: "每天", tips: ["避免加工食品", "自己烹饪"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "每日监测", description: "每天同一时间测量血压", duration: "5分钟", frequency: "每天", tips: ["记录数值", "注意变化趋势"], isCompleted: false)
        ]
    }

    private func generateAgeSpecificScreeningSteps(age: Int) -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "年度体检", description: "进行全面的健康检查", duration: "半天", frequency: "每年", tips: ["包含血液检查", "心电图等"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "专项筛查", description: "根据年龄进行特定筛查", duration: "按需", frequency: "按医嘱", tips: ["结肠镜、乳腺检查等", "遵医嘱安排"], isCompleted: false)
        ]
    }

    private func generateWomenHealthSteps(age: Int) -> [ActionStep] {
        return [
            ActionStep(stepNumber: 1, title: "妇科检查", description: "定期妇科和乳腺检查", duration: "1小时", frequency: "每年", tips: ["宫颈癌筛查", "乳腺超声"], isCompleted: false),
            ActionStep(stepNumber: 2, title: "骨密度检测", description: "检查骨质疏松风险", duration: "30分钟", frequency: "每2年", tips: ["特别是绝经后", "补充钙和维生素D"], isCompleted: false)
        ]
    }
}
