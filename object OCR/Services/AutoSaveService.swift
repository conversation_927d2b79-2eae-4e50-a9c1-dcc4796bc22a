//
//  AutoSaveService.swift
//  object OCR
//
//  Created by apple on 2025/7/30.
//

import SwiftUI
import Foundation
import Combine

/// 自动保存服务 - 管理应用数据的自动保存和文件操作
class AutoSaveService: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var isAutoSaveEnabled: Bool = true
    @Published var saveInterval: TimeInterval = 30.0 // 30秒自动保存
    @Published var lastSaveTime: Date?
    @Published var isSaving: Bool = false
    @Published var saveStatus: SaveStatus = .idle
    
    // MARK: - 私有属性
    
    private var autoSaveTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    private let fileManager = FileManager.default
    private let documentsDirectory: URL
    
    // MARK: - 保存状态枚举
    
    enum SaveStatus {
        case idle
        case saving
        case success
        case failed(Error)
        
        var message: String {
            switch self {
            case .idle:
                return "就绪"
            case .saving:
                return "保存中..."
            case .success:
                return "保存成功"
            case .failed(let error):
                return "保存失败: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - 文件类型枚举
    
    enum FileType: String, CaseIterable {
        case userProfile = "user_profile"
        case healthData = "health_data"
        case aiChatHistory = "ai_chat_history"
        case healthReports = "health_reports"
        case settings = "app_settings"
        case languagePreferences = "language_preferences"
        
        var fileName: String {
            return "\(self.rawValue).json"
        }
        
        var displayName: String {
            switch self {
            case .userProfile:
                return "用户资料"
            case .healthData:
                return "健康数据"
            case .aiChatHistory:
                return "AI对话记录"
            case .healthReports:
                return "健康报告"
            case .settings:
                return "应用设置"
            case .languagePreferences:
                return "语言偏好"
            }
        }
    }
    
    // MARK: - 初始化
    
    init() {
        // 获取文档目录
        self.documentsDirectory = fileManager.urls(for: .documentDirectory, 
                                                 in: .userDomainMask).first!
        
        // 创建应用数据文件夹
        createAppDataDirectory()
        
        // 加载自动保存设置
        loadAutoSaveSettings()
        
        // 启动自动保存
        startAutoSave()
        
        // 监听应用生命周期
        setupAppLifecycleObservers()
    }
    
    deinit {
        stopAutoSave()
    }
    
    // MARK: - 公共方法
    
    /// 手动保存所有数据
    func saveAllData() async {
        await MainActor.run {
            isSaving = true
            saveStatus = .saving
        }
        
        do {
            // 保存各种类型的数据
            try await saveUserProfile()
            try await saveHealthData()
            try await saveAIChatHistory()
            try await saveHealthReports()
            try await saveAppSettings()
            try await saveLanguagePreferences()
            
            await MainActor.run {
                lastSaveTime = Date()
                saveStatus = .success
                isSaving = false
            }
            
            // 3秒后重置状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                self.saveStatus = .idle
            }
            
        } catch {
            await MainActor.run {
                saveStatus = .failed(error)
                isSaving = false
            }
        }
    }
    
    /// 保存特定类型的数据
    func saveData<T: Codable>(data: T, type: FileType) async throws {
        let url = getFileURL(for: type)
        let jsonData = try JSONEncoder().encode(data)
        try jsonData.write(to: url)
    }
    
    /// 加载特定类型的数据
    func loadData<T: Codable>(type: FileType, as dataType: T.Type) async throws -> T? {
        let url = getFileURL(for: type)
        
        guard fileManager.fileExists(atPath: url.path) else {
            return nil
        }
        
        let jsonData = try Data(contentsOf: url)
        return try JSONDecoder().decode(dataType, from: jsonData)
    }
    
    /// 删除特定类型的数据文件
    func deleteData(type: FileType) async throws {
        let url = getFileURL(for: type)
        
        if fileManager.fileExists(atPath: url.path) {
            try fileManager.removeItem(at: url)
        }
    }
    
    /// 获取所有保存的文件信息
    func getAllSavedFiles() -> [SavedFileInfo] {
        var files: [SavedFileInfo] = []
        
        for fileType in FileType.allCases {
            let url = getFileURL(for: fileType)
            
            if fileManager.fileExists(atPath: url.path) {
                do {
                    let attributes = try fileManager.attributesOfItem(atPath: url.path)
                    let fileSize = attributes[.size] as? Int64 ?? 0
                    let modificationDate = attributes[.modificationDate] as? Date ?? Date()
                    
                    let fileInfo = SavedFileInfo(
                        type: fileType,
                        url: url,
                        size: fileSize,
                        lastModified: modificationDate
                    )
                    files.append(fileInfo)
                } catch {
                    print("获取文件信息失败: \(error)")
                }
            }
        }
        
        return files.sorted { $0.lastModified > $1.lastModified }
    }
    
    /// 导出所有数据到指定目录
    func exportAllData(to destinationURL: URL) async throws {
        let appDataURL = getAppDataDirectory()
        
        // 创建导出文件夹
        let exportURL = destinationURL.appendingPathComponent("HealthApp_Export_\(Date().timeIntervalSince1970)")
        try fileManager.createDirectory(at: exportURL, withIntermediateDirectories: true)
        
        // 复制所有文件
        let files = try fileManager.contentsOfDirectory(at: appDataURL, includingPropertiesForKeys: nil)
        
        for file in files {
            let destinationFile = exportURL.appendingPathComponent(file.lastPathComponent)
            try fileManager.copyItem(at: file, to: destinationFile)
        }
    }
    
    /// 从备份恢复数据
    func restoreFromBackup(backupURL: URL) async throws {
        let appDataURL = getAppDataDirectory()
        
        // 备份当前数据
        let backupCurrentURL = appDataURL.appendingPathComponent("backup_\(Date().timeIntervalSince1970)")
        try fileManager.createDirectory(at: backupCurrentURL, withIntermediateDirectories: true)
        
        let currentFiles = try fileManager.contentsOfDirectory(at: appDataURL, includingPropertiesForKeys: nil)
        for file in currentFiles {
            let backupFile = backupCurrentURL.appendingPathComponent(file.lastPathComponent)
            try fileManager.copyItem(at: file, to: backupFile)
        }
        
        // 清空当前数据
        for file in currentFiles {
            try fileManager.removeItem(at: file)
        }
        
        // 恢复备份数据
        let backupFiles = try fileManager.contentsOfDirectory(at: backupURL, includingPropertiesForKeys: nil)
        for file in backupFiles {
            let restoreFile = appDataURL.appendingPathComponent(file.lastPathComponent)
            try fileManager.copyItem(at: file, to: restoreFile)
        }
    }
    
    // MARK: - 自动保存控制
    
    /// 启动自动保存
    func startAutoSave() {
        guard isAutoSaveEnabled else { return }
        
        stopAutoSave() // 先停止现有的定时器
        
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: saveInterval, repeats: true) { _ in
            Task {
                await self.saveAllData()
            }
        }
    }
    
    /// 停止自动保存
    func stopAutoSave() {
        autoSaveTimer?.invalidate()
        autoSaveTimer = nil
    }
    
    /// 更新自动保存设置
    func updateAutoSaveSettings(enabled: Bool, interval: TimeInterval) {
        isAutoSaveEnabled = enabled
        saveInterval = interval
        
        // 保存设置
        UserDefaults.standard.set(enabled, forKey: "AutoSaveEnabled")
        UserDefaults.standard.set(interval, forKey: "AutoSaveInterval")
        
        // 重启自动保存
        if enabled {
            startAutoSave()
        } else {
            stopAutoSave()
        }
    }
}

// MARK: - 私有方法扩展

private extension AutoSaveService {
    
    /// 创建应用数据目录
    func createAppDataDirectory() {
        let appDataURL = getAppDataDirectory()
        
        if !fileManager.fileExists(atPath: appDataURL.path) {
            try? fileManager.createDirectory(at: appDataURL, withIntermediateDirectories: true)
        }
    }
    
    /// 获取应用数据目录
    func getAppDataDirectory() -> URL {
        return documentsDirectory.appendingPathComponent("HealthAppData")
    }
    
    /// 获取特定文件类型的URL
    func getFileURL(for type: FileType) -> URL {
        return getAppDataDirectory().appendingPathComponent(type.fileName)
    }
    
    /// 加载自动保存设置
    func loadAutoSaveSettings() {
        isAutoSaveEnabled = UserDefaults.standard.bool(forKey: "AutoSaveEnabled")
        if UserDefaults.standard.object(forKey: "AutoSaveEnabled") == nil {
            isAutoSaveEnabled = true // 默认启用
        }
        
        let savedInterval = UserDefaults.standard.double(forKey: "AutoSaveInterval")
        if savedInterval > 0 {
            saveInterval = savedInterval
        }
    }
    
    /// 设置应用生命周期观察者
    func setupAppLifecycleObservers() {
        // 应用进入后台时保存
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { _ in
                Task {
                    await self.saveAllData()
                }
            }
            .store(in: &cancellables)
        
        // 应用即将终止时保存
        NotificationCenter.default.publisher(for: UIApplication.willTerminateNotification)
            .sink { _ in
                Task {
                    await self.saveAllData()
                }
            }
            .store(in: &cancellables)
    }
    
    /// 保存用户资料
    func saveUserProfile() async throws {
        // 这里应该从UserProfileService获取数据
        // 暂时使用空字典作为示例
        let profileData: [String: Any] = [:]
        try await saveData(data: profileData, type: .userProfile)
    }
    
    /// 保存健康数据
    func saveHealthData() async throws {
        // 这里应该从HealthDataService获取数据
        let healthData: [String: Any] = [:]
        try await saveData(data: healthData, type: .healthData)
    }
    
    /// 保存AI对话记录
    func saveAIChatHistory() async throws {
        // 这里应该从AIDigitalDoctorService获取数据
        let chatHistory: [String: Any] = [:]
        try await saveData(data: chatHistory, type: .aiChatHistory)
    }
    
    /// 保存健康报告
    func saveHealthReports() async throws {
        // 这里应该从HealthReportService获取数据
        let reports: [String: Any] = [:]
        try await saveData(data: reports, type: .healthReports)
    }
    
    /// 保存应用设置
    func saveAppSettings() async throws {
        let settings: [String: Any] = [
            "autoSaveEnabled": isAutoSaveEnabled,
            "saveInterval": saveInterval,
            "lastSaveTime": lastSaveTime?.timeIntervalSince1970 ?? 0
        ]
        try await saveData(data: settings, type: .settings)
    }
    
    /// 保存语言偏好
    func saveLanguagePreferences() async throws {
        // 这里应该从MultiLanguageService获取数据
        let languageData: [String: Any] = [:]
        try await saveData(data: languageData, type: .languagePreferences)
    }
}

// MARK: - 保存文件信息结构

struct SavedFileInfo: Identifiable {
    let id = UUID()
    let type: AutoSaveService.FileType
    let url: URL
    let size: Int64
    let lastModified: Date
    
    var formattedSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: lastModified)
    }
}
