//
//  AIConversationService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation
import SwiftUI

/// AI对话服务 - 处理智能健康咨询对话
class AIConversationService: ObservableObject {
    
    @Published var conversationHistory: [ConversationMessage] = []
    @Published var isProcessing = false
    @Published var currentContext: ConversationContext?
    
    // MARK: - 对话数据结构
    
    struct ConversationMessage: Identifiable, Codable {
        let id = UUID()
        let content: String
        let isFromUser: Bool
        let timestamp: Date
        let messageType: MessageType
        let attachments: [MessageAttachment]
        let confidence: Double?
        let suggestedActions: [SuggestedAction]
        
        enum MessageType: String, Codable {
            case text = "文本"
            case symptomReport = "症状报告"
            case healthData = "健康数据"
            case recommendation = "建议"
            case warning = "警告"
            case followUp = "随访"
            case emergency = "紧急"
        }
    }
    
    struct MessageAttachment: Identifiable, Codable {
        let id = UUID()
        let type: AttachmentType
        let data: String
        let description: String
        
        enum AttachmentType: String, Codable {
            case healthData = "健康数据"
            case image = "图片"
            case document = "文档"
            case chart = "图表"
        }
    }
    
    struct SuggestedAction: Identifiable, Codable {
        let id = UUID()
        let title: String
        let description: String
        let actionType: ActionType
        let priority: Priority
        
        enum ActionType: String, Codable {
            case scheduleAppointment = "预约就诊"
            case trackSymptom = "症状跟踪"
            case takeAction = "采取行动"
            case getMoreInfo = "获取更多信息"
            case emergency = "紧急处理"
        }
        
        enum Priority: String, Codable {
            case urgent = "紧急"
            case high = "高"
            case medium = "中"
            case low = "低"
        }
    }
    
    struct ConversationContext: Codable {
        let userId: UUID
        let sessionId: UUID
        let startTime: Date
        let currentTopic: String
        let userProfile: ExtendedUserProfile?
        let recentHealthData: [HealthDataEntry]
        let conversationGoal: ConversationGoal
        let riskFactors: [String]
        
        enum ConversationGoal: String, Codable {
            case generalConsultation = "一般咨询"
            case symptomAssessment = "症状评估"
            case healthMonitoring = "健康监测"
            case medicationGuidance = "用药指导"
            case lifestyleAdvice = "生活方式建议"
            case emergencyTriage = "紧急分诊"
        }
    }
    
    // MARK: - 主要对话处理方法
    
    /// 处理用户消息并生成AI回复
    func processUserMessage(
        _ message: String,
        userProfile: ExtendedUserProfile?,
        recentHealthData: [HealthDataEntry] = []
    ) async -> ConversationMessage {
        
        isProcessing = true
        defer { isProcessing = false }
        
        // 添加用户消息到历史记录
        let userMessage = ConversationMessage(
            content: message,
            isFromUser: true,
            timestamp: Date(),
            messageType: .text,
            attachments: [],
            confidence: nil,
            suggestedActions: []
        )
        
        await MainActor.run {
            conversationHistory.append(userMessage)
        }
        
        // 分析消息意图
        let intent = analyzeMessageIntent(message)
        
        // 更新对话上下文
        updateConversationContext(intent: intent, userProfile: userProfile, recentHealthData: recentHealthData)
        
        // 生成AI回复
        let aiResponse = await generateAIResponse(
            userMessage: message,
            intent: intent,
            context: currentContext
        )
        
        await MainActor.run {
            conversationHistory.append(aiResponse)
        }
        
        return aiResponse
    }
    
    // MARK: - 消息意图分析
    
    private func analyzeMessageIntent(_ message: String) -> MessageIntent {
        let lowercaseMessage = message.lowercased()
        
        // 紧急情况关键词
        let emergencyKeywords = ["胸痛", "呼吸困难", "昏迷", "大出血", "严重头痛", "中毒", "过敏反应"]
        if emergencyKeywords.contains(where: { lowercaseMessage.contains($0) }) {
            return MessageIntent(
                type: .emergency,
                confidence: 0.9,
                keywords: emergencyKeywords.filter { lowercaseMessage.contains($0) },
                urgency: .critical
            )
        }
        
        // 症状报告关键词
        let symptomKeywords = ["疼痛", "发烧", "咳嗽", "头痛", "恶心", "疲劳", "失眠", "焦虑", "抑郁"]
        if symptomKeywords.contains(where: { lowercaseMessage.contains($0) }) {
            return MessageIntent(
                type: .symptomReport,
                confidence: 0.8,
                keywords: symptomKeywords.filter { lowercaseMessage.contains($0) },
                urgency: .medium
            )
        }
        
        // 健康数据查询关键词
        let dataKeywords = ["血压", "血糖", "体重", "心率", "睡眠", "运动", "数据", "记录"]
        if dataKeywords.contains(where: { lowercaseMessage.contains($0) }) {
            return MessageIntent(
                type: .healthDataInquiry,
                confidence: 0.7,
                keywords: dataKeywords.filter { lowercaseMessage.contains($0) },
                urgency: .low
            )
        }
        
        // 建议请求关键词
        let adviceKeywords = ["建议", "怎么办", "如何", "应该", "推荐", "治疗", "改善"]
        if adviceKeywords.contains(where: { lowercaseMessage.contains($0) }) {
            return MessageIntent(
                type: .adviceRequest,
                confidence: 0.7,
                keywords: adviceKeywords.filter { lowercaseMessage.contains($0) },
                urgency: .low
            )
        }
        
        // 默认为一般咨询
        return MessageIntent(
            type: .generalInquiry,
            confidence: 0.5,
            keywords: [],
            urgency: .low
        )
    }
    
    struct MessageIntent {
        let type: IntentType
        let confidence: Double
        let keywords: [String]
        let urgency: UrgencyLevel
        
        enum IntentType {
            case emergency
            case symptomReport
            case healthDataInquiry
            case adviceRequest
            case medicationQuestion
            case appointmentRequest
            case generalInquiry
        }
        
        enum UrgencyLevel {
            case critical
            case high
            case medium
            case low
        }
    }
    
    // MARK: - AI回复生成
    
    private func generateAIResponse(
        userMessage: String,
        intent: MessageIntent,
        context: ConversationContext?
    ) async -> ConversationMessage {
        
        // 模拟AI处理时间
        try? await Task.sleep(nanoseconds: 1_500_000_000)
        
        switch intent.type {
        case .emergency:
            return generateEmergencyResponse(keywords: intent.keywords, context: context)
        case .symptomReport:
            return generateSymptomAssessmentResponse(keywords: intent.keywords, context: context)
        case .healthDataInquiry:
            return generateHealthDataResponse(keywords: intent.keywords, context: context)
        case .adviceRequest:
            return generateAdviceResponse(keywords: intent.keywords, context: context)
        case .medicationQuestion:
            return generateMedicationResponse(keywords: intent.keywords, context: context)
        case .appointmentRequest:
            return generateAppointmentResponse(context: context)
        case .generalInquiry:
            return generateGeneralResponse(userMessage: userMessage, context: context)
        }
    }
    
    private func generateEmergencyResponse(keywords: [String], context: ConversationContext?) -> ConversationMessage {
        let emergencyAdvice = """
        ⚠️ 紧急情况警告 ⚠️
        
        根据您描述的症状（\(keywords.joined(separator: "、"))），这可能是需要立即医疗关注的紧急情况。
        
        请立即采取以下行动：
        1. 如果情况严重，立即拨打120急救电话
        2. 前往最近的急诊科
        3. 在等待期间，保持冷静，避免剧烈活动
        4. 如有可能，请家人或朋友陪同
        
        这不是专业医疗诊断，请务必寻求专业医疗帮助。
        """
        
        let suggestedActions = [
            SuggestedAction(
                title: "拨打急救电话",
                description: "立即拨打120寻求紧急医疗帮助",
                actionType: .emergency,
                priority: .urgent
            ),
            SuggestedAction(
                title: "前往急诊科",
                description: "立即前往最近的医院急诊科",
                actionType: .emergency,
                priority: .urgent
            )
        ]
        
        return ConversationMessage(
            content: emergencyAdvice,
            isFromUser: false,
            timestamp: Date(),
            messageType: .emergency,
            attachments: [],
            confidence: 0.95,
            suggestedActions: suggestedActions
        )
    }
    
    private func generateSymptomAssessmentResponse(keywords: [String], context: ConversationContext?) -> ConversationMessage {
        let symptomAnalysis = """
        基于您提到的症状（\(keywords.joined(separator: "、"))），我来为您进行初步评估：
        
        症状分析：
        • 这些症状可能与多种原因相关
        • 建议记录症状的持续时间、严重程度和触发因素
        • 观察是否有其他伴随症状
        
        建议措施：
        1. 继续监测症状变化
        2. 保持充足休息和水分摄入
        3. 避免可能的触发因素
        4. 如症状持续或加重，请咨询医生
        
        请注意：这只是初步评估，不能替代专业医疗诊断。
        """
        
        let suggestedActions = [
            SuggestedAction(
                title: "症状跟踪",
                description: "记录症状的变化和严重程度",
                actionType: .trackSymptom,
                priority: .high
            ),
            SuggestedAction(
                title: "预约医生",
                description: "如症状持续，建议预约医生咨询",
                actionType: .scheduleAppointment,
                priority: .medium
            )
        ]
        
        return ConversationMessage(
            content: symptomAnalysis,
            isFromUser: false,
            timestamp: Date(),
            messageType: .symptomReport,
            attachments: [],
            confidence: 0.8,
            suggestedActions: suggestedActions
        )
    }
    
    private func generateHealthDataResponse(keywords: [String], context: ConversationContext?) -> ConversationMessage {
        guard let context = context, !context.recentHealthData.isEmpty else {
            return ConversationMessage(
                content: "我注意到您询问关于\(keywords.joined(separator: "、"))的信息。目前我没有看到您的相关健康数据记录。建议您开始记录这些健康指标，这样我就能为您提供更个性化的分析和建议。",
                isFromUser: false,
                timestamp: Date(),
                messageType: .healthData,
                attachments: [],
                confidence: 0.6,
                suggestedActions: [
                    SuggestedAction(
                        title: "开始记录数据",
                        description: "开始记录相关健康数据",
                        actionType: .takeAction,
                        priority: .medium
                    )
                ]
            )
        }
        
        let dataAnalysis = analyzeRecentHealthData(context.recentHealthData, keywords: keywords)
        
        return ConversationMessage(
            content: dataAnalysis,
            isFromUser: false,
            timestamp: Date(),
            messageType: .healthData,
            attachments: [],
            confidence: 0.85,
            suggestedActions: [
                SuggestedAction(
                    title: "查看详细趋势",
                    description: "查看健康数据的详细趋势分析",
                    actionType: .getMoreInfo,
                    priority: .low
                )
            ]
        )
    }
    
    private func generateAdviceResponse(keywords: [String], context: ConversationContext?) -> ConversationMessage {
        let advice = """
        基于您的询问，我为您提供以下建议：
        
        个性化建议：
        • 根据您的健康状况，建议采取循序渐进的方法
        • 重点关注生活方式的改善
        • 保持规律的健康监测
        
        具体行动：
        1. 制定可行的健康目标
        2. 建立良好的生活习惯
        3. 定期评估进展情况
        4. 必要时寻求专业指导
        
        记住：持续性比完美性更重要，小的改变也能带来大的收益。
        """
        
        return ConversationMessage(
            content: advice,
            isFromUser: false,
            timestamp: Date(),
            messageType: .recommendation,
            attachments: [],
            confidence: 0.75,
            suggestedActions: [
                SuggestedAction(
                    title: "制定行动计划",
                    description: "根据建议制定具体的行动计划",
                    actionType: .takeAction,
                    priority: .medium
                )
            ]
        )
    }
    
    private func generateMedicationResponse(keywords: [String], context: ConversationContext?) -> ConversationMessage {
        let medicationGuidance = """
        关于用药问题，我需要提醒您：
        
        重要提醒：
        • 任何药物的使用都应该在医生指导下进行
        • 不要自行调整药物剂量或停药
        • 注意药物的副作用和相互作用
        
        一般建议：
        1. 按时按量服药
        2. 记录用药情况和身体反应
        3. 定期复查和评估
        4. 有疑问及时咨询医生或药师
        
        请务必咨询专业医疗人员获得准确的用药指导。
        """
        
        return ConversationMessage(
            content: medicationGuidance,
            isFromUser: false,
            timestamp: Date(),
            messageType: .warning,
            attachments: [],
            confidence: 0.9,
            suggestedActions: [
                SuggestedAction(
                    title: "咨询医生",
                    description: "就用药问题咨询专业医生",
                    actionType: .scheduleAppointment,
                    priority: .high
                )
            ]
        )
    }
    
    private func generateAppointmentResponse(context: ConversationContext?) -> ConversationMessage {
        let appointmentInfo = """
        关于预约就诊，我为您提供以下信息：
        
        预约建议：
        • 根据您的症状选择合适的科室
        • 准备好相关的健康记录和检查报告
        • 列出想要咨询的问题清单
        
        就诊准备：
        1. 整理近期的健康数据
        2. 记录症状的详细情况
        3. 准备既往病史和用药情况
        4. 选择合适的就诊时间
        
        我可以帮您整理健康记录，为就诊做好准备。
        """
        
        return ConversationMessage(
            content: appointmentInfo,
            isFromUser: false,
            timestamp: Date(),
            messageType: .followUp,
            attachments: [],
            confidence: 0.8,
            suggestedActions: [
                SuggestedAction(
                    title: "整理健康记录",
                    description: "整理近期健康数据为就诊做准备",
                    actionType: .takeAction,
                    priority: .medium
                )
            ]
        )
    }
    
    private func generateGeneralResponse(userMessage: String, context: ConversationContext?) -> ConversationMessage {
        let generalResponse = """
        感谢您的咨询。作为您的AI健康助手，我很乐意为您提供帮助。
        
        我可以协助您：
        • 分析健康数据和趋势
        • 提供个性化健康建议
        • 评估症状和风险因素
        • 制定健康改善计划
        
        请告诉我您具体想了解什么，或者您目前关心的健康问题，我会尽力为您提供有用的信息和建议。
        
        请记住：我的建议仅供参考，不能替代专业医疗诊断和治疗。
        """
        
        return ConversationMessage(
            content: generalResponse,
            isFromUser: false,
            timestamp: Date(),
            messageType: .text,
            attachments: [],
            confidence: 0.7,
            suggestedActions: [
                SuggestedAction(
                    title: "了解更多功能",
                    description: "探索AI健康助手的更多功能",
                    actionType: .getMoreInfo,
                    priority: .low
                )
            ]
        )
    }
    
    // MARK: - 辅助方法
    
    private func updateConversationContext(
        intent: MessageIntent,
        userProfile: ExtendedUserProfile?,
        recentHealthData: [HealthDataEntry]
    ) {
        let goal: ConversationContext.ConversationGoal
        switch intent.type {
        case .emergency:
            goal = .emergencyTriage
        case .symptomReport:
            goal = .symptomAssessment
        case .healthDataInquiry:
            goal = .healthMonitoring
        case .medicationQuestion:
            goal = .medicationGuidance
        case .adviceRequest:
            goal = .lifestyleAdvice
        default:
            goal = .generalConsultation
        }
        
        currentContext = ConversationContext(
            userId: userProfile?.id ?? UUID(),
            sessionId: UUID(),
            startTime: Date(),
            currentTopic: intent.keywords.joined(separator: ", "),
            userProfile: userProfile,
            recentHealthData: recentHealthData,
            conversationGoal: goal,
            riskFactors: extractRiskFactors(from: userProfile)
        )
    }
    
    private func extractRiskFactors(from profile: ExtendedUserProfile?) -> [String] {
        guard let profile = profile else { return [] }
        
        var riskFactors: [String] = []
        
        if profile.basicInfo.age > 65 {
            riskFactors.append("高龄")
        }
        
        if profile.basicInfo.bmi > 30 {
            riskFactors.append("肥胖")
        }
        
        if profile.lifestyle.smokingHistory.currentStatus == .currentSmoker {
            riskFactors.append("吸烟")
        }
        
        if !profile.medicalHistory.chronicDiseases.isEmpty {
            riskFactors.append("慢性疾病史")
        }
        
        return riskFactors
    }
    
    private func analyzeRecentHealthData(_ data: [HealthDataEntry], keywords: [String]) -> String {
        // 简化的数据分析
        let recentEntries = data.prefix(10)
        
        if recentEntries.isEmpty {
            return "目前没有相关的健康数据记录。建议您开始记录相关健康指标。"
        }
        
        return """
        基于您最近的健康数据记录：
        
        数据概览：
        • 最近记录了\(recentEntries.count)条健康数据
        • 数据类型包括：\(keywords.joined(separator: "、"))
        • 整体趋势：需要更多数据来分析长期趋势
        
        建议：
        1. 继续保持规律的数据记录
        2. 关注数据的变化趋势
        3. 结合生活方式因素分析数据
        
        如需详细分析，请查看健康趋势报告。
        """
    }
    
    /// 清除对话历史
    func clearConversationHistory() {
        conversationHistory.removeAll()
        currentContext = nil
    }
    
    /// 导出对话记录
    func exportConversationHistory() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        
        var exportText = "=== AI健康助手对话记录 ===\n\n"
        
        for message in conversationHistory {
            let sender = message.isFromUser ? "用户" : "AI助手"
            let timestamp = formatter.string(from: message.timestamp)
            
            exportText += "[\(timestamp)] \(sender):\n"
            exportText += "\(message.content)\n\n"
        }
        
        return exportText
    }
}
