//
//  HealthWisdomService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation

// MARK: - 健康智慧服务（多语言健康格言和建议）
class HealthWisdomService: ObservableObject {
    @Published var dailyWisdom: HealthWisdom?
    @Published var currentLanguage: Language = .chinese
    
    // MARK: - 维吾尔语健康格言库
    private let uyghurHealthWisdoms = [
        HealthWisdom(
            chinese: "健康是人生最大的财富",
            uyghur: "ساغلاملىق ئىنساننىڭ ئەڭ چوڭ بايلىقى",
            english: "Health is the greatest wealth of life",
            category: .general,
            keywords: ["健康", "财富", "人生"]
        ),
        HealthWisdom(
            chinese: "早睡早起身体好",
            uyghur: "بالدۇر ئۇخلاش، بالدۇر تۇرۇش تەننى ساغلام قىلىدۇ",
            english: "Early to bed and early to rise makes one healthy",
            category: .sleep,
            keywords: ["睡眠", "作息", "健康"]
        ),
        HealthWisdom(
            chinese: "饭后百步走，活到九十九",
            uyghur: "تاماقتىن كېيىن يۈز قەدەم مېڭىش، توقسان توققۇزغىچە ياشىتىدۇ",
            english: "A hundred steps after meals helps you live to ninety-nine",
            category: .exercise,
            keywords: ["运动", "散步", "长寿"]
        ),
        HealthWisdom(
            chinese: "心情愉快是健康的良药",
            uyghur: "كۆڭۈل خۇشاللىقى ساغلاملىقنىڭ ئەڭ ياخشى دورىسى",
            english: "A happy mood is the best medicine for health",
            category: .mental,
            keywords: ["心情", "快乐", "心理健康"]
        ),
        HealthWisdom(
            chinese: "多喝水，身体好",
            uyghur: "كۆپ سۇ ئىچىش تەننى ساغلام قىلىدۇ",
            english: "Drinking more water keeps the body healthy",
            category: .nutrition,
            keywords: ["喝水", "水分", "健康"]
        ),
        HealthWisdom(
            chinese: "笑一笑，十年少",
            uyghur: "بىر كۈلۈش ئون يىل ياشارتىدۇ",
            english: "A smile makes you ten years younger",
            category: .mental,
            keywords: ["微笑", "快乐", "年轻"]
        ),
        HealthWisdom(
            chinese: "药补不如食补",
            uyghur: "دورا ئىچىشتىن كۆرە ياخشى يېمەك يېيىش ياخشى",
            english: "Food therapy is better than medicine",
            category: .nutrition,
            keywords: ["食疗", "营养", "药物"]
        ),
        HealthWisdom(
            chinese: "生命在于运动",
            uyghur: "ھايات ھەرىكەتتە",
            english: "Life lies in movement",
            category: .exercise,
            keywords: ["生命", "运动", "活力"]
        ),
        HealthWisdom(
            chinese: "预防胜于治疗",
            uyghur: "ئالدىنى ئېلىش داۋالاشتىن ياخشى",
            english: "Prevention is better than cure",
            category: .preventive,
            keywords: ["预防", "治疗", "健康管理"]
        ),
        HealthWisdom(
            chinese: "身心健康，家庭幸福",
            uyghur: "تەن-روھ ساغلاملىقى، ئائىلە بەختى",
            english: "Physical and mental health brings family happiness",
            category: .general,
            keywords: ["身心健康", "家庭", "幸福"]
        )
    ]
    
    // MARK: - 健康建议库
    private let healthTips = [
        // 饮食建议
        HealthTip(
            category: .nutrition,
            condition: .obesity,
            chinese: "控制热量摄入，增加蔬菜水果比例，少食多餐",
            uyghur: "كالورىيە مىقدارىنى كونتروللاش، كۆكتات مېۋە نىسبىتىنى ئاشۇرۇش، ئاز يەپ كۆپ قېتىم يېيىش",
            english: "Control calorie intake, increase vegetables and fruits, eat smaller frequent meals",
            priority: .high
        ),
        HealthTip(
            category: .nutrition,
            condition: .diabetes,
            chinese: "控制碳水化合物摄入，选择低升糖指数食物，定时定量进餐",
            uyghur: "ئۇگلېۋودان مىقدارىنى كونتروللاش، تۆۋەن قاندا شېكىرى كۆتۈرۈش ئىندېكسلىق يېمەكلىكلەرنى تاللاش",
            english: "Control carbohydrate intake, choose low glycemic index foods, eat regular meals",
            priority: .high
        ),
        HealthTip(
            category: .nutrition,
            condition: .hypertension,
            chinese: "减少钠盐摄入，增加钾元素丰富的食物，控制体重",
            uyghur: "تۇز مىقدارىنى ئازايتىش، كالىي ئېلېمېنتى مول يېمەكلىكلەرنى ئاشۇرۇش، ئېغىرلىقنى كونتروللاش",
            english: "Reduce sodium intake, increase potassium-rich foods, control weight",
            priority: .high
        ),
        
        // 运动建议
        HealthTip(
            category: .exercise,
            condition: .sedentary,
            chinese: "每天至少30分钟中等强度运动，如快走、游泳或骑车",
            uyghur: "ھەر كۈنى ئاز دېگەندە 30 مىنۇت ئوتتۇراھال كۈچلۈكتىكى ماشىق، مەسىلەن تېز مېڭىش، ئۈزۈش ياكى ۋېلوسىپېد مىنىش",
            english: "At least 30 minutes of moderate exercise daily, such as brisk walking, swimming or cycling",
            priority: .high
        ),
        HealthTip(
            category: .exercise,
            condition: .arthritis,
            chinese: "选择低冲击性运动，如水中运动、太极拳或瑜伽",
            uyghur: "تۆۋەن تەسىر كۈچلۈك ماشىقلارنى تاللاش، مەسىلەن سۇدىكى ماشىق، تايجى ياكى يوگا",
            english: "Choose low-impact exercises like water exercises, Tai Chi or yoga",
            priority: .medium
        ),
        
        // 心理健康建议
        HealthTip(
            category: .mental,
            condition: .stress,
            chinese: "学习放松技巧，如深呼吸、冥想或听音乐",
            uyghur: "بوشىشىش تېخنىكىسىنى ئۆگىنىش، مەسىلەن چوڭقۇر نەپەس ئېلىش، مۇراقىبە ياكى مۇزىكا ئاڭلاش",
            english: "Learn relaxation techniques like deep breathing, meditation or listening to music",
            priority: .medium
        ),
        HealthTip(
            category: .mental,
            condition: .depression,
            chinese: "保持社交联系，参与喜欢的活动，寻求专业帮助",
            uyghur: "ئىجتىمائىي ئالاقىنى ساقلاش، ياقتۇرىدىغان پائالىيەتلەرگە قاتنىشىش، كەسپىي ياردەم ئىزدەش",
            english: "Maintain social connections, engage in enjoyable activities, seek professional help",
            priority: .high
        ),
        
        // 睡眠建议
        HealthTip(
            category: .sleep,
            condition: .insomnia,
            chinese: "建立规律作息，睡前避免咖啡因和电子设备",
            uyghur: "قانۇنلۇق ئىش-دەم ۋاقتىنى قۇرۇش، ئۇخلاشتىن بۇرۇن كوفېئىن ۋە ئېلېكتروننىق ئۈسكۈنىلەردىن ساقلىنىش",
            english: "Establish regular sleep schedule, avoid caffeine and electronic devices before bed",
            priority: .medium
        )
    ]
    
    // MARK: - 公共方法
    
    /// 获取每日健康格言
    func getDailyWisdom() -> HealthWisdom {
        let today = Calendar.current.startOfDay(for: Date())
        let dayOfYear = Calendar.current.ordinality(of: .day, in: .year, for: today) ?? 1
        let index = (dayOfYear - 1) % uyghurHealthWisdoms.count
        
        let wisdom = uyghurHealthWisdoms[index]
        DispatchQueue.main.async {
            self.dailyWisdom = wisdom
        }
        return wisdom
    }
    
    /// 根据用户健康状况获取个性化建议
    func getPersonalizedTips(for profile: ExtendedUserProfile) -> [HealthTip] {
        var personalizedTips: [HealthTip] = []
        
        // 根据BMI给出建议
        let height = profile.basicInfo.height / 100
        let weight = profile.basicInfo.weight
        if height > 0 && weight > 0 {
            let bmi = weight / (height * height)
            if bmi >= 28 {
                personalizedTips.append(contentsOf: healthTips.filter { $0.condition == .obesity })
            }
        }
        
        // 根据慢性疾病给出建议
        for disease in profile.medicalHistory.chronicDiseases {
            switch disease {
            case .diabetes:
                personalizedTips.append(contentsOf: healthTips.filter { $0.condition == .diabetes })
            case .hypertension:
                personalizedTips.append(contentsOf: healthTips.filter { $0.condition == .hypertension })
            default:
                break
            }
        }
        
        // 根据活动水平给出建议
        if profile.physicalActivity.currentActivityLevel == .sedentary {
            personalizedTips.append(contentsOf: healthTips.filter { $0.condition == .sedentary })
        }
        
        // 根据心理健康状况给出建议
        if profile.mentalHealth.stressLevel == .high || profile.mentalHealth.stressLevel == .veryHigh {
            personalizedTips.append(contentsOf: healthTips.filter { $0.condition == .stress })
        }
        
        // 根据睡眠质量给出建议
        if profile.lifestyle.sleepPattern.sleepQuality == .poor || profile.lifestyle.sleepPattern.sleepQuality == .veryPoor {
            personalizedTips.append(contentsOf: healthTips.filter { $0.condition == .insomnia })
        }
        
        return Array(Set(personalizedTips)).sorted { $0.priority.rawValue > $1.priority.rawValue }
    }
    
    /// 获取随机健康格言
    func getRandomWisdom(category: WisdomCategory? = nil) -> HealthWisdom {
        let filteredWisdoms = category == nil ? uyghurHealthWisdoms : uyghurHealthWisdoms.filter { $0.category == category }
        return filteredWisdoms.randomElement() ?? uyghurHealthWisdoms[0]
    }
    
    /// 搜索健康格言
    func searchWisdom(keyword: String) -> [HealthWisdom] {
        return uyghurHealthWisdoms.filter { wisdom in
            wisdom.chinese.contains(keyword) ||
            wisdom.uyghur.contains(keyword) ||
            wisdom.english.lowercased().contains(keyword.lowercased()) ||
            wisdom.keywords.contains { $0.contains(keyword) }
        }
    }
}

// MARK: - 数据模型

struct HealthWisdom: Identifiable, Codable {
    let id = UUID()
    let chinese: String
    let uyghur: String
    let english: String
    let category: WisdomCategory
    let keywords: [String]
    
    func getText(for language: Language) -> String {
        switch language {
        case .chinese: return chinese
        case .uyghur: return uyghur
        case .english: return english
        default: return chinese
        }
    }
}

enum WisdomCategory: String, CaseIterable, Codable {
    case general = "通用"
    case nutrition = "营养"
    case exercise = "运动"
    case sleep = "睡眠"
    case mental = "心理"
    case preventive = "预防"
}

struct HealthTip: Identifiable, Codable, Hashable {
    let id = UUID()
    let category: HealthCategory
    let condition: HealthCondition
    let chinese: String
    let uyghur: String
    let english: String
    let priority: Priority
    
    func getText(for language: Language) -> String {
        switch language {
        case .chinese: return chinese
        case .uyghur: return uyghur
        case .english: return english
        default: return chinese
        }
    }
    
    static func == (lhs: HealthTip, rhs: HealthTip) -> Bool {
        lhs.id == rhs.id
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}

enum HealthCondition: String, CaseIterable, Codable {
    case obesity = "肥胖"
    case diabetes = "糖尿病"
    case hypertension = "高血压"
    case sedentary = "久坐"
    case arthritis = "关节炎"
    case stress = "压力"
    case depression = "抑郁"
    case insomnia = "失眠"
    case general = "通用"
}

enum Priority: Int, CaseIterable, Codable {
    case low = 1
    case medium = 2
    case high = 3
    case urgent = 4
}
