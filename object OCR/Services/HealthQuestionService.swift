//
//  HealthQuestionService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation

class HealthQuestionService: ObservableObject {
    @Published var questions: [HealthQuestion] = []
    @Published var currentQuestionIndex = 0
    @Published var userAnswers: [UserAnswer] = []
    @Published var isCompleted = false
    
    init() {
        loadQuestions()
    }
    
    var currentQuestion: HealthQuestion? {
        guard currentQuestionIndex < questions.count else { return nil }
        return questions[currentQuestionIndex]
    }
    
    // 加载预设问题
    private func loadQuestions() {
        questions = [
            // 身体健康问题
            HealthQuestion(
                title: "您的整体身体状况如何？",
                description: "请根据您最近一个月的感受选择",
                type: .singleChoice,
                category: .physical,
                options: [
                    AnswerOption(text: "非常好", score: 5, description: "精力充沛，很少生病"),
                    AnswerOption(text: "比较好", score: 4, description: "身体状况良好"),
                    AnswerOption(text: "一般", score: 3, description: "偶尔感到不适"),
                    AnswerOption(text: "比较差", score: 2, description: "经常感到疲劳"),
                    AnswerOption(text: "很差", score: 1, description: "身体状况令人担忧")
                ],
                isRequired: true,
                order: 1
            ),
            
            HealthQuestion(
                title: "您是否有慢性疾病？",
                description: "如高血压、糖尿病、心脏病等",
                type: .multipleChoice,
                category: .physical,
                options: [
                    AnswerOption(text: "无慢性疾病", score: 5, description: "身体健康"),
                    AnswerOption(text: "高血压", score: 2, description: "需要控制血压"),
                    AnswerOption(text: "糖尿病", score: 2, description: "需要控制血糖"),
                    AnswerOption(text: "心脏病", score: 1, description: "需要特别关注"),
                    AnswerOption(text: "其他慢性疾病", score: 2, description: "需要定期检查")
                ],
                isRequired: true,
                order: 2
            ),
            
            // 心理健康问题
            HealthQuestion(
                title: "您最近的心情状态如何？",
                description: "请根据最近两周的感受选择",
                type: .singleChoice,
                category: .mental,
                options: [
                    AnswerOption(text: "心情愉快", score: 5, description: "积极乐观"),
                    AnswerOption(text: "心情平静", score: 4, description: "情绪稳定"),
                    AnswerOption(text: "偶尔焦虑", score: 3, description: "有时感到压力"),
                    AnswerOption(text: "经常焦虑", score: 2, description: "压力较大"),
                    AnswerOption(text: "情绪低落", score: 1, description: "需要关注心理健康")
                ],
                isRequired: true,
                order: 3
            ),
            
            HealthQuestion(
                title: "您的压力水平如何？",
                description: "1-10分，1分表示无压力，10分表示压力极大",
                type: .scale,
                category: .mental,
                options: [],
                minScore: 1,
                maxScore: 10,
                isRequired: true,
                order: 4
            ),
            
            // 生活习惯问题
            HealthQuestion(
                title: "您是否有吸烟习惯？",
                description: "包括电子烟",
                type: .singleChoice,
                category: .lifestyle,
                options: [
                    AnswerOption(text: "从不吸烟", score: 5, description: "很好的习惯"),
                    AnswerOption(text: "已戒烟", score: 4, description: "恭喜您戒烟成功"),
                    AnswerOption(text: "偶尔吸烟", score: 2, description: "建议完全戒烟"),
                    AnswerOption(text: "经常吸烟", score: 1, description: "强烈建议戒烟")
                ],
                isRequired: true,
                order: 5
            ),
            
            HealthQuestion(
                title: "您的饮酒频率如何？",
                description: "请选择最符合您情况的选项",
                type: .singleChoice,
                category: .lifestyle,
                options: [
                    AnswerOption(text: "从不饮酒", score: 5, description: "很好的习惯"),
                    AnswerOption(text: "偶尔饮酒", score: 4, description: "适量饮酒"),
                    AnswerOption(text: "每周饮酒", score: 3, description: "注意控制量"),
                    AnswerOption(text: "每天饮酒", score: 2, description: "建议减少饮酒"),
                    AnswerOption(text: "大量饮酒", score: 1, description: "需要戒酒")
                ],
                isRequired: true,
                order: 6
            ),
            
            // 营养饮食问题
            HealthQuestion(
                title: "您的饮食习惯如何？",
                description: "请根据您的日常饮食情况选择",
                type: .singleChoice,
                category: .nutrition,
                options: [
                    AnswerOption(text: "营养均衡", score: 5, description: "饮食很健康"),
                    AnswerOption(text: "比较均衡", score: 4, description: "饮食较为健康"),
                    AnswerOption(text: "一般", score: 3, description: "有改善空间"),
                    AnswerOption(text: "不太均衡", score: 2, description: "需要调整饮食"),
                    AnswerOption(text: "很不均衡", score: 1, description: "饮食习惯需要改善")
                ],
                isRequired: true,
                order: 7
            ),
            
            HealthQuestion(
                title: "您每天喝水量大约是多少？",
                description: "包括各种饮品中的水分",
                type: .singleChoice,
                category: .nutrition,
                options: [
                    AnswerOption(text: "2升以上", score: 5, description: "水分充足"),
                    AnswerOption(text: "1.5-2升", score: 4, description: "水分较充足"),
                    AnswerOption(text: "1-1.5升", score: 3, description: "基本够用"),
                    AnswerOption(text: "0.5-1升", score: 2, description: "水分不足"),
                    AnswerOption(text: "少于0.5升", score: 1, description: "严重缺水")
                ],
                isRequired: true,
                order: 8
            ),
            
            // 睡眠质量问题
            HealthQuestion(
                title: "您每天的睡眠时间是多少？",
                description: "包括夜间睡眠和午休",
                type: .singleChoice,
                category: .sleep,
                options: [
                    AnswerOption(text: "7-9小时", score: 5, description: "睡眠时间充足"),
                    AnswerOption(text: "6-7小时", score: 4, description: "睡眠时间较充足"),
                    AnswerOption(text: "5-6小时", score: 3, description: "睡眠时间一般"),
                    AnswerOption(text: "4-5小时", score: 2, description: "睡眠不足"),
                    AnswerOption(text: "少于4小时", score: 1, description: "严重睡眠不足")
                ],
                isRequired: true,
                order: 9
            ),
            
            HealthQuestion(
                title: "您的睡眠质量如何？",
                description: "请根据您的睡眠感受选择",
                type: .singleChoice,
                category: .sleep,
                options: [
                    AnswerOption(text: "睡眠很好", score: 5, description: "容易入睡，睡得很深"),
                    AnswerOption(text: "睡眠较好", score: 4, description: "一般能睡好"),
                    AnswerOption(text: "睡眠一般", score: 3, description: "偶尔失眠"),
                    AnswerOption(text: "睡眠较差", score: 2, description: "经常失眠"),
                    AnswerOption(text: "睡眠很差", score: 1, description: "严重失眠")
                ],
                isRequired: true,
                order: 10
            ),
            
            // 运动锻炼问题
            HealthQuestion(
                title: "您每周的运动频率如何？",
                description: "包括各种形式的体育锻炼",
                type: .singleChoice,
                category: .exercise,
                options: [
                    AnswerOption(text: "每天运动", score: 5, description: "运动习惯很好"),
                    AnswerOption(text: "每周4-6次", score: 4, description: "运动频率较高"),
                    AnswerOption(text: "每周2-3次", score: 3, description: "运动频率一般"),
                    AnswerOption(text: "每周1次", score: 2, description: "运动不足"),
                    AnswerOption(text: "很少运动", score: 1, description: "缺乏运动")
                ],
                isRequired: true,
                order: 11
            ),
            
            HealthQuestion(
                title: "您每次运动的时长大约是多少？",
                description: "单次运动的持续时间",
                type: .singleChoice,
                category: .exercise,
                options: [
                    AnswerOption(text: "60分钟以上", score: 5, description: "运动时长充足"),
                    AnswerOption(text: "30-60分钟", score: 4, description: "运动时长较好"),
                    AnswerOption(text: "15-30分钟", score: 3, description: "运动时长一般"),
                    AnswerOption(text: "5-15分钟", score: 2, description: "运动时长不足"),
                    AnswerOption(text: "少于5分钟", score: 1, description: "运动时长太短")
                ],
                isRequired: true,
                order: 12
            ),

            // 工作健康问题
            HealthQuestion(
                title: "您的工作压力水平如何？",
                description: "请根据您最近的工作感受选择",
                type: .singleChoice,
                category: .work,
                options: [
                    AnswerOption(text: "压力很小", score: 5, description: "工作轻松愉快"),
                    AnswerOption(text: "压力适中", score: 4, description: "工作有挑战但可控"),
                    AnswerOption(text: "压力较大", score: 3, description: "工作压力明显"),
                    AnswerOption(text: "压力很大", score: 2, description: "工作压力沉重"),
                    AnswerOption(text: "压力极大", score: 1, description: "工作压力难以承受")
                ],
                isRequired: true,
                order: 13
            ),

            HealthQuestion(
                title: "您的工作时长如何？",
                description: "每天的工作时间",
                type: .singleChoice,
                category: .work,
                options: [
                    AnswerOption(text: "8小时以内", score: 5, description: "工作时间合理"),
                    AnswerOption(text: "8-10小时", score: 4, description: "工作时间较长"),
                    AnswerOption(text: "10-12小时", score: 3, description: "工作时间过长"),
                    AnswerOption(text: "12小时以上", score: 2, description: "工作时间严重超标"),
                    AnswerOption(text: "经常加班", score: 1, description: "工作负荷过重")
                ],
                isRequired: true,
                order: 14
            ),

            // 社交健康问题
            HealthQuestion(
                title: "您的社交活动频率如何？",
                description: "与朋友、家人的交往情况",
                type: .singleChoice,
                category: .social,
                options: [
                    AnswerOption(text: "经常社交", score: 5, description: "社交生活丰富"),
                    AnswerOption(text: "定期社交", score: 4, description: "社交生活较好"),
                    AnswerOption(text: "偶尔社交", score: 3, description: "社交生活一般"),
                    AnswerOption(text: "很少社交", score: 2, description: "社交活动不足"),
                    AnswerOption(text: "几乎不社交", score: 1, description: "缺乏社交活动")
                ],
                isRequired: true,
                order: 15
            ),

            HealthQuestion(
                title: "您对人际关系的满意度如何？",
                description: "包括家庭、朋友、同事关系",
                type: .singleChoice,
                category: .social,
                options: [
                    AnswerOption(text: "非常满意", score: 5, description: "人际关系和谐"),
                    AnswerOption(text: "比较满意", score: 4, description: "人际关系较好"),
                    AnswerOption(text: "一般", score: 3, description: "人际关系普通"),
                    AnswerOption(text: "不太满意", score: 2, description: "人际关系有问题"),
                    AnswerOption(text: "很不满意", score: 1, description: "人际关系紧张")
                ],
                isRequired: true,
                order: 16
            ),

            // 环境健康问题
            HealthQuestion(
                title: "您对居住环境的满意度如何？",
                description: "包括空气质量、噪音、绿化等",
                type: .singleChoice,
                category: .environment,
                options: [
                    AnswerOption(text: "非常满意", score: 5, description: "居住环境优良"),
                    AnswerOption(text: "比较满意", score: 4, description: "居住环境较好"),
                    AnswerOption(text: "一般", score: 3, description: "居住环境普通"),
                    AnswerOption(text: "不太满意", score: 2, description: "居住环境有问题"),
                    AnswerOption(text: "很不满意", score: 1, description: "居住环境较差")
                ],
                isRequired: true,
                order: 17
            ),

            HealthQuestion(
                title: "您的工作环境如何？",
                description: "包括办公环境、通风、照明等",
                type: .singleChoice,
                category: .environment,
                options: [
                    AnswerOption(text: "环境很好", score: 5, description: "工作环境优良"),
                    AnswerOption(text: "环境较好", score: 4, description: "工作环境不错"),
                    AnswerOption(text: "环境一般", score: 3, description: "工作环境普通"),
                    AnswerOption(text: "环境较差", score: 2, description: "工作环境有问题"),
                    AnswerOption(text: "环境很差", score: 1, description: "工作环境恶劣")
                ],
                isRequired: true,
                order: 18
            ),

            // 预防保健问题
            HealthQuestion(
                title: "您多久进行一次体检？",
                description: "定期健康检查的频率",
                type: .singleChoice,
                category: .preventive,
                options: [
                    AnswerOption(text: "每年体检", score: 5, description: "体检习惯很好"),
                    AnswerOption(text: "每两年体检", score: 4, description: "体检频率较好"),
                    AnswerOption(text: "偶尔体检", score: 3, description: "体检不够规律"),
                    AnswerOption(text: "很少体检", score: 2, description: "缺乏体检意识"),
                    AnswerOption(text: "从不体检", score: 1, description: "没有体检习惯")
                ],
                isRequired: true,
                order: 19
            ),

            HealthQuestion(
                title: "您是否按时接种疫苗？",
                description: "包括流感疫苗等预防性疫苗",
                type: .singleChoice,
                category: .preventive,
                options: [
                    AnswerOption(text: "按时接种", score: 5, description: "预防意识很强"),
                    AnswerOption(text: "大部分接种", score: 4, description: "预防意识较强"),
                    AnswerOption(text: "偶尔接种", score: 3, description: "预防意识一般"),
                    AnswerOption(text: "很少接种", score: 2, description: "预防意识不足"),
                    AnswerOption(text: "从不接种", score: 1, description: "缺乏预防意识")
                ],
                isRequired: true,
                order: 20
            )
        ]

        // 按order排序
        questions.sort { $0.order < $1.order }
    }
    
    // 回答问题
    func answerQuestion(selectedOptionIds: [UUID] = [], scaleValue: Double? = nil, textAnswer: String? = nil) {
        guard let currentQuestion = currentQuestion else { return }
        
        let answer = UserAnswer(
            questionId: currentQuestion.id,
            selectedOptionIds: selectedOptionIds,
            scaleValue: scaleValue,
            textAnswer: textAnswer
        )
        
        userAnswers.append(answer)
        
        if currentQuestionIndex < questions.count - 1 {
            currentQuestionIndex += 1
        } else {
            isCompleted = true
        }
    }
    
    // 上一题
    func previousQuestion() {
        if currentQuestionIndex > 0 {
            currentQuestionIndex -= 1
            // 移除当前问题的答案
            if let lastAnswer = userAnswers.last {
                userAnswers.removeAll { $0.id == lastAnswer.id }
            }
        }
    }
    
    // 重置问卷
    func resetQuestionnaire() {
        currentQuestionIndex = 0
        userAnswers.removeAll()
        isCompleted = false
    }
    
    // 计算总分
    func calculateTotalScore() -> Int {
        var totalScore = 0
        
        for answer in userAnswers {
            if let question = questions.first(where: { $0.id == answer.questionId }) {
                if question.type == .scale {
                    // 量表题直接使用量表值
                    totalScore += Int(answer.scaleValue ?? 0)
                } else {
                    // 其他题型使用选项分数
                    for optionId in answer.selectedOptionIds {
                        if let option = question.options.first(where: { $0.id == optionId }) {
                            totalScore += option.score
                        }
                    }
                }
            }
        }
        
        return totalScore
    }
    
    // 计算分类得分
    func calculateCategoryScores() -> [HealthCategory: Int] {
        var categoryScores: [HealthCategory: Int] = [:]
        
        for category in HealthCategory.allCases {
            categoryScores[category] = 0
        }
        
        for answer in userAnswers {
            if let question = questions.first(where: { $0.id == answer.questionId }) {
                let category = question.category
                
                if question.type == .scale {
                    categoryScores[category, default: 0] += Int(answer.scaleValue ?? 0)
                } else {
                    for optionId in answer.selectedOptionIds {
                        if let option = question.options.first(where: { $0.id == optionId }) {
                            categoryScores[category, default: 0] += option.score
                        }
                    }
                }
            }
        }
        
        return categoryScores
    }
}
