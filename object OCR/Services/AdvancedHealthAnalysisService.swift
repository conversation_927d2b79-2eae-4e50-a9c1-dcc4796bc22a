//
//  AdvancedHealthAnalysisService.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation
import SwiftUI

/// 高级健康分析服务 - 提供更精确的健康评估算法
class AdvancedHealthAnalysisService: ObservableObject {
    
    // MARK: - 心血管风险评估算法
    
    /// 使用Framingham风险评分计算心血管疾病风险
    func calculateFraminghamRisk(profile: ExtendedUserProfile) -> HealthRisk {
        var score = 0.0
        let age = profile.basicInfo.age
        let gender = profile.basicInfo.gender
        let isSmoker = profile.lifestyle.smokingHistory.currentStatus == .currentSmoker
        
        // 年龄评分
        if gender == .male {
            if age >= 20 && age <= 34 { score += -9 }
            else if age >= 35 && age <= 39 { score += -4 }
            else if age >= 40 && age <= 44 { score += 0 }
            else if age >= 45 && age <= 49 { score += 3 }
            else if age >= 50 && age <= 54 { score += 6 }
            else if age >= 55 && age <= 59 { score += 8 }
            else if age >= 60 && age <= 64 { score += 10 }
            else if age >= 65 && age <= 69 { score += 11 }
            else if age >= 70 && age <= 74 { score += 12 }
            else if age >= 75 { score += 13 }
        } else {
            if age >= 20 && age <= 34 { score += -7 }
            else if age >= 35 && age <= 39 { score += -3 }
            else if age >= 40 && age <= 44 { score += 0 }
            else if age >= 45 && age <= 49 { score += 3 }
            else if age >= 50 && age <= 54 { score += 6 }
            else if age >= 55 && age <= 59 { score += 8 }
            else if age >= 60 && age <= 64 { score += 10 }
            else if age >= 65 && age <= 69 { score += 12 }
            else if age >= 70 && age <= 74 { score += 14 }
            else if age >= 75 { score += 16 }
        }
        
        // 胆固醇评分
        let cholesterol = profile.medicalHistory.cholesterol.totalCholesterol
        if cholesterol < 160 { score += 0 }
        else if cholesterol < 200 { score += 4 }
        else if cholesterol < 240 { score += 7 }
        else if cholesterol < 280 { score += 9 }
        else { score += 11 }
        
        // HDL胆固醇评分
        let hdl = profile.medicalHistory.cholesterol.hdlCholesterol
        if hdl >= 60 { score += -1 }
        else if hdl >= 50 { score += 0 }
        else if hdl >= 40 { score += 1 }
        else { score += 2 }
        
        // 血压评分
        let systolic = profile.medicalHistory.bloodPressure.systolic
        if systolic < 120 { score += 0 }
        else if systolic < 130 { score += 0 }
        else if systolic < 140 { score += 1 }
        else if systolic < 160 { score += 1 }
        else { score += 2 }
        
        // 吸烟评分
        if isSmoker {
            if gender == .male {
                if age < 40 { score += 8 }
                else if age < 50 { score += 5 }
                else if age < 60 { score += 3 }
                else if age < 70 { score += 1 }
                else { score += 1 }
            } else {
                if age < 40 { score += 9 }
                else if age < 50 { score += 7 }
                else if age < 60 { score += 4 }
                else if age < 70 { score += 2 }
                else { score += 1 }
            }
        }
        
        // 糖尿病评分
        if profile.medicalHistory.chronicDiseases.contains(.diabetes) {
            score += gender == .male ? 2 : 3
        }
        
        // 计算10年风险概率
        let probability = calculateFraminghamProbability(score: score, gender: gender)
        
        let riskLevel: RiskLevel
        if probability < 0.1 { riskLevel = .low }
        else if probability < 0.2 { riskLevel = .moderate }
        else if probability < 0.3 { riskLevel = .high }
        else { riskLevel = .critical }
        
        return HealthRisk(
            condition: "心血管疾病",
            level: riskLevel,
            probability: probability,
            factors: generateCardiovascularRiskFactors(profile: profile),
            preventionStrategies: generateCardiovascularPreventionStrategies(riskLevel: riskLevel)
        )
    }
    
    private func calculateFraminghamProbability(score: Double, gender: Gender) -> Double {
        // Framingham风险评分转换为概率的查找表
        let maleRiskTable: [Double: Double] = [
            -3: 0.01, -2: 0.01, -1: 0.01, 0: 0.01, 1: 0.01,
            2: 0.02, 3: 0.02, 4: 0.02, 5: 0.03, 6: 0.04,
            7: 0.05, 8: 0.06, 9: 0.08, 10: 0.10, 11: 0.12,
            12: 0.16, 13: 0.20, 14: 0.25, 15: 0.30, 16: 0.36,
            17: 0.42
        ]
        
        let femaleRiskTable: [Double: Double] = [
            -2: 0.01, -1: 0.01, 0: 0.01, 1: 0.01, 2: 0.01,
            3: 0.02, 4: 0.02, 5: 0.03, 6: 0.04, 7: 0.04,
            8: 0.05, 9: 0.07, 10: 0.08, 11: 0.10, 12: 0.13,
            13: 0.15, 14: 0.18, 15: 0.22, 16: 0.27, 17: 0.32
        }
        
        let table = gender == .male ? maleRiskTable : femaleRiskTable
        let roundedScore = round(score)
        
        return table[roundedScore] ?? (roundedScore > 17 ? 0.40 : 0.01)
    }
    
    // MARK: - 糖尿病风险评估算法
    
    /// 使用Finnish Diabetes Risk Score (FINDRISC)评估糖尿病风险
    func calculateDiabetesRisk(profile: ExtendedUserProfile) -> HealthRisk {
        var score = 0
        let age = profile.basicInfo.age
        let bmi = profile.basicInfo.bmi
        let waistCircumference = profile.basicInfo.waistCircumference ?? 0
        let gender = profile.basicInfo.gender
        
        // 年龄评分
        if age < 45 { score += 0 }
        else if age < 55 { score += 2 }
        else if age < 65 { score += 3 }
        else { score += 4 }
        
        // BMI评分
        if bmi < 25 { score += 0 }
        else if bmi < 30 { score += 1 }
        else { score += 3 }
        
        // 腰围评分
        if gender == .male {
            if waistCircumference < 94 { score += 0 }
            else if waistCircumference < 102 { score += 3 }
            else { score += 4 }
        } else {
            if waistCircumference < 80 { score += 0 }
            else if waistCircumference < 88 { score += 3 }
            else { score += 4 }
        }
        
        // 体力活动评分
        let exerciseFrequency = profile.physicalActivity.exerciseFrequency
        if exerciseFrequency == .daily || exerciseFrequency == .fourToSixTimesPerWeek {
            score += 0
        } else {
            score += 2
        }
        
        // 蔬菜水果摄入评分
        let vegetableIntake = profile.nutrition.dietaryHabits.vegetableIntake
        if vegetableIntake == .high {
            score += 0
        } else {
            score += 1
        }
        
        // 高血压用药史评分
        if profile.medicalHistory.bloodPressure.level != .normal {
            score += 2
        }
        
        // 高血糖史评分
        let bloodSugar = profile.medicalHistory.bloodSugar.fastingGlucose
        if bloodSugar > 6.1 {
            score += 5
        }
        
        // 家族糖尿病史评分
        if profile.medicalHistory.familyHistory.contains(.diabetes) {
            score += 5
        }
        
        // 计算风险等级
        let riskLevel: RiskLevel
        let probability: Double
        
        if score < 7 {
            riskLevel = .low
            probability = 0.01
        } else if score < 12 {
            riskLevel = .moderate
            probability = 0.04
        } else if score < 15 {
            riskLevel = .high
            probability = 0.17
        } else if score < 21 {
            riskLevel = .high
            probability = 0.33
        } else {
            riskLevel = .critical
            probability = 0.50
        }
        
        return HealthRisk(
            condition: "2型糖尿病",
            level: riskLevel,
            probability: probability,
            factors: generateDiabetesRiskFactors(profile: profile),
            preventionStrategies: generateDiabetesPreventionStrategies(riskLevel: riskLevel)
        )
    }
    
    // MARK: - 癌症风险评估算法
    
    /// 综合癌症风险评估
    func calculateCancerRisk(profile: ExtendedUserProfile) -> HealthRisk {
        var totalRiskScore = 0.0
        var riskFactors: [RiskFactor] = []
        
        // 年龄因素
        let age = profile.basicInfo.age
        let ageRisk = calculateAgeRelatedCancerRisk(age: age)
        totalRiskScore += ageRisk
        if ageRisk > 0.1 {
            riskFactors.append(RiskFactor(
                name: "年龄因素",
                impact: age > 65 ? .high : .moderate,
                modifiable: false,
                description: "年龄增长会增加多种癌症的风险"
            ))
        }
        
        // 吸烟因素
        let smokingRisk = calculateSmokingCancerRisk(smokingHistory: profile.lifestyle.smokingHistory)
        totalRiskScore += smokingRisk
        if smokingRisk > 0.05 {
            riskFactors.append(RiskFactor(
                name: "吸烟史",
                impact: .high,
                modifiable: true,
                description: "吸烟显著增加肺癌、膀胱癌等多种癌症风险"
            ))
        }
        
        // 饮酒因素
        let alcoholRisk = calculateAlcoholCancerRisk(alcoholConsumption: profile.lifestyle.alcoholConsumption)
        totalRiskScore += alcoholRisk
        if alcoholRisk > 0.03 {
            riskFactors.append(RiskFactor(
                name: "饮酒习惯",
                impact: .moderate,
                modifiable: true,
                description: "过量饮酒增加肝癌、乳腺癌等风险"
            ))
        }
        
        // BMI因素
        let bmiRisk = calculateBMICancerRisk(bmi: profile.basicInfo.bmi)
        totalRiskScore += bmiRisk
        if bmiRisk > 0.02 {
            riskFactors.append(RiskFactor(
                name: "体重指数",
                impact: .moderate,
                modifiable: true,
                description: "肥胖增加多种癌症的风险"
            ))
        }
        
        // 家族史因素
        let familyHistoryRisk = calculateFamilyHistoryCancerRisk(familyHistory: profile.medicalHistory.familyHistory)
        totalRiskScore += familyHistoryRisk
        if familyHistoryRisk > 0.05 {
            riskFactors.append(RiskFactor(
                name: "家族病史",
                impact: .high,
                modifiable: false,
                description: "家族癌症史显著增加患癌风险"
            ))
        }
        
        // 环境因素
        let environmentRisk = calculateEnvironmentalCancerRisk(environment: profile.lifestyle.livingEnvironment)
        totalRiskScore += environmentRisk
        if environmentRisk > 0.02 {
            riskFactors.append(RiskFactor(
                name: "环境因素",
                impact: .moderate,
                modifiable: true,
                description: "环境污染和职业暴露增加癌症风险"
            ))
        }
        
        // 确定风险等级
        let riskLevel: RiskLevel
        if totalRiskScore < 0.1 { riskLevel = .low }
        else if totalRiskScore < 0.2 { riskLevel = .moderate }
        else if totalRiskScore < 0.35 { riskLevel = .high }
        else { riskLevel = .critical }
        
        return HealthRisk(
            condition: "癌症",
            level: riskLevel,
            probability: min(totalRiskScore, 0.5), // 限制最大概率为50%
            factors: riskFactors,
            preventionStrategies: generateCancerPreventionStrategies(riskLevel: riskLevel)
        )
    }
    
    // MARK: - 心理健康风险评估算法
    
    /// 使用PHQ-9和GAD-7量表评估心理健康风险
    func calculateMentalHealthRisk(profile: ExtendedUserProfile) -> HealthRisk {
        let mentalHealth = profile.mentalHealth
        var riskScore = 0.0
        var riskFactors: [RiskFactor] = []
        
        // 抑郁症状评估
        let depressionScore = calculateDepressionScore(mentalHealth: mentalHealth)
        riskScore += depressionScore
        
        // 焦虑症状评估
        let anxietyScore = calculateAnxietyScore(mentalHealth: mentalHealth)
        riskScore += anxietyScore
        
        // 压力水平评估
        let stressScore = calculateStressScore(stressFactors: mentalHealth.stressFactors)
        riskScore += stressScore
        
        // 睡眠质量评估
        let sleepScore = calculateSleepImpactScore(sleepPattern: profile.lifestyle.sleepPattern)
        riskScore += sleepScore
        
        // 社会支持评估
        let socialScore = calculateSocialSupportScore(socialSupport: mentalHealth.socialSupport)
        riskScore -= socialScore // 社会支持是保护因素
        
        // 生成风险因素
        if depressionScore > 0.3 {
            riskFactors.append(RiskFactor(
                name: "抑郁症状",
                impact: .high,
                modifiable: true,
                description: "存在明显的抑郁症状，需要专业关注"
            ))
        }
        
        if anxietyScore > 0.3 {
            riskFactors.append(RiskFactor(
                name: "焦虑症状",
                impact: .high,
                modifiable: true,
                description: "存在明显的焦虑症状，影响日常生活"
            ))
        }
        
        if stressScore > 0.4 {
            riskFactors.append(RiskFactor(
                name: "高压力水平",
                impact: .moderate,
                modifiable: true,
                description: "长期高压力状态影响心理健康"
            ))
        }
        
        // 确定风险等级
        let riskLevel: RiskLevel
        if riskScore < 0.2 { riskLevel = .low }
        else if riskScore < 0.4 { riskLevel = .moderate }
        else if riskScore < 0.6 { riskLevel = .high }
        else { riskLevel = .critical }
        
        return HealthRisk(
            condition: "心理健康问题",
            level: riskLevel,
            probability: min(max(riskScore, 0.0), 1.0),
            factors: riskFactors,
            preventionStrategies: generateMentalHealthPreventionStrategies(riskLevel: riskLevel)
        )
    }
    
    // MARK: - 辅助计算方法
    
    private func calculateAgeRelatedCancerRisk(age: Int) -> Double {
        if age < 30 { return 0.01 }
        else if age < 40 { return 0.02 }
        else if age < 50 { return 0.05 }
        else if age < 60 { return 0.10 }
        else if age < 70 { return 0.15 }
        else { return 0.20 }
    }
    
    private func calculateSmokingCancerRisk(smokingHistory: SmokingHistory) -> Double {
        switch smokingHistory.currentStatus {
        case .neverSmoked: return 0.0
        case .formerSmoker:
            let yearsQuit = smokingHistory.yearsQuit ?? 0
            if yearsQuit > 15 { return 0.02 }
            else if yearsQuit > 10 { return 0.05 }
            else { return 0.08 }
        case .currentSmoker:
            let packsPerDay = smokingHistory.packsPerDay ?? 1.0
            return min(0.15 * packsPerDay, 0.25)
        }
    }
    
    private func calculateAlcoholCancerRisk(alcoholConsumption: AlcoholConsumption) -> Double {
        switch alcoholConsumption.frequency {
        case .never: return 0.0
        case .rarely: return 0.01
        case .weekly: return 0.02
        case .daily:
            let unitsPerDay = alcoholConsumption.unitsPerDay ?? 1.0
            return min(0.03 * unitsPerDay, 0.10)
        }
    }
    
    private func calculateBMICancerRisk(bmi: Double) -> Double {
        if bmi < 18.5 { return 0.01 }
        else if bmi < 25 { return 0.0 }
        else if bmi < 30 { return 0.02 }
        else if bmi < 35 { return 0.05 }
        else { return 0.08 }
    }
    
    private func calculateFamilyHistoryCancerRisk(familyHistory: [ChronicDisease]) -> Double {
        let cancerHistory = familyHistory.filter { disease in
            // 假设有癌症相关的疾病类型
            return disease.rawValue.contains("癌") || disease.rawValue.contains("肿瘤")
        }
        return Double(cancerHistory.count) * 0.03
    }
    
    private func calculateEnvironmentalCancerRisk(environment: LivingEnvironment) -> Double {
        var risk = 0.0
        
        switch environment.airQuality {
        case .excellent, .good: risk += 0.0
        case .moderate: risk += 0.01
        case .poor: risk += 0.03
        case .veryPoor: risk += 0.05
        }
        
        // 可以添加更多环境因素的评估
        
        return risk
    }
    
    private func calculateDepressionScore(mentalHealth: MentalHealthProfile) -> Double {
        // 基于PHQ-9量表的简化评估
        var score = 0.0
        
        switch mentalHealth.moodLevel {
        case .veryPoor: score += 0.4
        case .poor: score += 0.3
        case .fair: score += 0.2
        case .good: score += 0.1
        case .excellent: score += 0.0
        }
        
        return score
    }
    
    private func calculateAnxietyScore(mentalHealth: MentalHealthProfile) -> Double {
        // 基于GAD-7量表的简化评估
        var score = 0.0
        
        switch mentalHealth.anxietyLevel {
        case .none: score += 0.0
        case .mild: score += 0.1
        case .moderate: score += 0.3
        case .severe: score += 0.5
        }
        
        return score
    }
    
    private func calculateStressScore(stressFactors: [StressFactor]) -> Double {
        let highStressFactors = stressFactors.filter { $0.intensity == .high || $0.intensity == .severe }
        return Double(highStressFactors.count) * 0.1
    }
    
    private func calculateSleepImpactScore(sleepPattern: SleepPattern) -> Double {
        let avgSleep = sleepPattern.averageHoursPerNight
        if avgSleep < 6 || avgSleep > 9 {
            return 0.2
        }
        return 0.0
    }
    
    private func calculateSocialSupportScore(socialSupport: SocialSupport) -> Double {
        var score = 0.0
        
        switch socialSupport.familySupport {
        case .excellent: score += 0.3
        case .good: score += 0.2
        case .moderate: score += 0.1
        case .poor, .veryPoor: score += 0.0
        }
        
        switch socialSupport.friendSupport {
        case .excellent: score += 0.2
        case .good: score += 0.15
        case .moderate: score += 0.1
        case .poor, .veryPoor: score += 0.0
        }
        
        return score
    }

    // MARK: - 风险因素生成方法

    private func generateCardiovascularRiskFactors(profile: ExtendedUserProfile) -> [RiskFactor] {
        var factors: [RiskFactor] = []

        // 年龄因素
        if profile.basicInfo.age > 45 {
            factors.append(RiskFactor(
                name: "年龄",
                impact: profile.basicInfo.age > 65 ? .high : .moderate,
                modifiable: false,
                description: "年龄增长是心血管疾病的重要风险因素"
            ))
        }

        // 吸烟
        if profile.lifestyle.smokingHistory.currentStatus == .currentSmoker {
            factors.append(RiskFactor(
                name: "吸烟",
                impact: .high,
                modifiable: true,
                description: "吸烟显著增加心血管疾病风险"
            ))
        }

        // 高血压
        if profile.medicalHistory.bloodPressure.level != .normal {
            factors.append(RiskFactor(
                name: "高血压",
                impact: .high,
                modifiable: true,
                description: "高血压是心血管疾病的主要风险因素"
            ))
        }

        // 高胆固醇
        if profile.medicalHistory.cholesterol.totalCholesterol > 200 {
            factors.append(RiskFactor(
                name: "高胆固醇",
                impact: .moderate,
                modifiable: true,
                description: "高胆固醇增加动脉硬化风险"
            ))
        }

        // 糖尿病
        if profile.medicalHistory.chronicDiseases.contains(.diabetes) {
            factors.append(RiskFactor(
                name: "糖尿病",
                impact: .high,
                modifiable: true,
                description: "糖尿病显著增加心血管并发症风险"
            ))
        }

        // 肥胖
        if profile.basicInfo.bmi > 30 {
            factors.append(RiskFactor(
                name: "肥胖",
                impact: .moderate,
                modifiable: true,
                description: "肥胖增加心血管疾病风险"
            ))
        }

        // 缺乏运动
        if profile.physicalActivity.exerciseFrequency == .rarely || profile.physicalActivity.exerciseFrequency == .never {
            factors.append(RiskFactor(
                name: "缺乏运动",
                impact: .moderate,
                modifiable: true,
                description: "缺乏体力活动增加心血管风险"
            ))
        }

        return factors
    }

    private func generateDiabetesRiskFactors(profile: ExtendedUserProfile) -> [RiskFactor] {
        var factors: [RiskFactor] = []

        // 年龄
        if profile.basicInfo.age > 45 {
            factors.append(RiskFactor(
                name: "年龄",
                impact: .moderate,
                modifiable: false,
                description: "45岁以上糖尿病风险增加"
            ))
        }

        // 超重或肥胖
        if profile.basicInfo.bmi > 25 {
            factors.append(RiskFactor(
                name: "超重/肥胖",
                impact: profile.basicInfo.bmi > 30 ? .high : .moderate,
                modifiable: true,
                description: "超重和肥胖是2型糖尿病的主要风险因素"
            ))
        }

        // 腹部肥胖
        let waistCircumference = profile.basicInfo.waistCircumference ?? 0
        let isAbdominalObesity = profile.basicInfo.gender == .male ? waistCircumference > 102 : waistCircumference > 88
        if isAbdominalObesity {
            factors.append(RiskFactor(
                name: "腹部肥胖",
                impact: .high,
                modifiable: true,
                description: "腹部脂肪堆积增加胰岛素抵抗风险"
            ))
        }

        // 缺乏体力活动
        if profile.physicalActivity.exerciseFrequency == .rarely || profile.physicalActivity.exerciseFrequency == .never {
            factors.append(RiskFactor(
                name: "缺乏运动",
                impact: .moderate,
                modifiable: true,
                description: "缺乏体力活动降低胰岛素敏感性"
            ))
        }

        // 不健康饮食
        if profile.nutrition.dietaryHabits.vegetableIntake == .low {
            factors.append(RiskFactor(
                name: "不健康饮食",
                impact: .moderate,
                modifiable: true,
                description: "缺乏蔬菜水果摄入增加糖尿病风险"
            ))
        }

        // 家族史
        if profile.medicalHistory.familyHistory.contains(.diabetes) {
            factors.append(RiskFactor(
                name: "家族史",
                impact: .high,
                modifiable: false,
                description: "糖尿病家族史显著增加患病风险"
            ))
        }

        // 高血压
        if profile.medicalHistory.bloodPressure.level != .normal {
            factors.append(RiskFactor(
                name: "高血压",
                impact: .moderate,
                modifiable: true,
                description: "高血压常与糖尿病并存"
            ))
        }

        return factors
    }

    // MARK: - 预防策略生成方法

    private func generateCardiovascularPreventionStrategies(riskLevel: RiskLevel) -> [PreventionStrategy] {
        var strategies: [PreventionStrategy] = []

        // 基础策略（适用于所有风险等级）
        strategies.append(PreventionStrategy(
            category: .lifestyle,
            title: "健康饮食",
            description: "采用地中海饮食模式，减少饱和脂肪和反式脂肪摄入",
            priority: .high,
            timeframe: .longTerm
        ))

        strategies.append(PreventionStrategy(
            category: .exercise,
            title: "规律运动",
            description: "每周至少150分钟中等强度有氧运动",
            priority: .high,
            timeframe: .longTerm
        ))

        strategies.append(PreventionStrategy(
            category: .lifestyle,
            title: "戒烟限酒",
            description: "完全戒烟，限制酒精摄入",
            priority: .high,
            timeframe: .immediate
        ))

        // 根据风险等级添加特定策略
        if riskLevel == .moderate || riskLevel == .high || riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .medical,
                title: "定期体检",
                description: "每6个月检查血压、血脂、血糖",
                priority: .high,
                timeframe: .shortTerm
            ))
        }

        if riskLevel == .high || riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .medical,
                title: "药物治疗",
                description: "根据医生建议使用降压药、降脂药等",
                priority: .high,
                timeframe: .immediate
            ))

            strategies.append(PreventionStrategy(
                category: .lifestyle,
                title: "压力管理",
                description: "学习压力管理技巧，保持心理健康",
                priority: .moderate,
                timeframe: .mediumTerm
            ))
        }

        if riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .medical,
                title: "专科咨询",
                description: "定期心血管专科医生随访",
                priority: .high,
                timeframe: .immediate
            ))
        }

        return strategies
    }

    private func generateDiabetesPreventionStrategies(riskLevel: RiskLevel) -> [PreventionStrategy] {
        var strategies: [PreventionStrategy] = []

        // 基础策略
        strategies.append(PreventionStrategy(
            category: .nutrition,
            title: "控制体重",
            description: "通过健康饮食和运动将BMI控制在正常范围",
            priority: .high,
            timeframe: .longTerm
        ))

        strategies.append(PreventionStrategy(
            category: .nutrition,
            title: "低GI饮食",
            description: "选择低血糖指数食物，控制碳水化合物摄入",
            priority: .high,
            timeframe: .longTerm
        ))

        strategies.append(PreventionStrategy(
            category: .exercise,
            title: "增加体力活动",
            description: "每天至少30分钟中等强度运动",
            priority: .high,
            timeframe: .shortTerm
        ))

        // 根据风险等级添加策略
        if riskLevel == .moderate || riskLevel == .high || riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .medical,
                title: "血糖监测",
                description: "定期监测空腹血糖和糖化血红蛋白",
                priority: .high,
                timeframe: .shortTerm
            ))
        }

        if riskLevel == .high || riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .medical,
                title: "预防性药物",
                description: "考虑使用二甲双胍等预防性药物",
                priority: .moderate,
                timeframe: .mediumTerm
            ))

            strategies.append(PreventionStrategy(
                category: .nutrition,
                title: "营养咨询",
                description: "寻求专业营养师指导制定个性化饮食计划",
                priority: .moderate,
                timeframe: .shortTerm
            ))
        }

        return strategies
    }

    private func generateCancerPreventionStrategies(riskLevel: RiskLevel) -> [PreventionStrategy] {
        var strategies: [PreventionStrategy] = []

        // 基础预防策略
        strategies.append(PreventionStrategy(
            category: .lifestyle,
            title: "戒烟",
            description: "完全戒烟，避免二手烟暴露",
            priority: .high,
            timeframe: .immediate
        ))

        strategies.append(PreventionStrategy(
            category: .nutrition,
            title: "健康饮食",
            description: "多吃蔬菜水果，减少加工肉类和红肉摄入",
            priority: .high,
            timeframe: .longTerm
        ))

        strategies.append(PreventionStrategy(
            category: .lifestyle,
            title: "限制酒精",
            description: "限制酒精摄入，女性每天不超过1杯，男性不超过2杯",
            priority: .moderate,
            timeframe: .longTerm
        ))

        strategies.append(PreventionStrategy(
            category: .exercise,
            title: "保持活跃",
            description: "规律运动，维持健康体重",
            priority: .moderate,
            timeframe: .longTerm
        ))

        // 根据风险等级添加策略
        if riskLevel == .moderate || riskLevel == .high || riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .medical,
                title: "定期筛查",
                description: "按年龄和风险进行癌症筛查",
                priority: .high,
                timeframe: .shortTerm
            ))
        }

        if riskLevel == .high || riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .medical,
                title: "遗传咨询",
                description: "考虑遗传咨询和基因检测",
                priority: .moderate,
                timeframe: .shortTerm
            ))

            strategies.append(PreventionStrategy(
                category: .lifestyle,
                title: "环境保护",
                description: "减少有害化学物质和辐射暴露",
                priority: .moderate,
                timeframe: .longTerm
            ))
        }

        return strategies
    }

    private func generateMentalHealthPreventionStrategies(riskLevel: RiskLevel) -> [PreventionStrategy] {
        var strategies: [PreventionStrategy] = []

        // 基础策略
        strategies.append(PreventionStrategy(
            category: .lifestyle,
            title: "规律作息",
            description: "保持规律的睡眠时间，每晚7-9小时",
            priority: .high,
            timeframe: .shortTerm
        ))

        strategies.append(PreventionStrategy(
            category: .exercise,
            title: "规律运动",
            description: "每周至少3次运动，有助于改善情绪",
            priority: .high,
            timeframe: .shortTerm
        ))

        strategies.append(PreventionStrategy(
            category: .lifestyle,
            title: "社交活动",
            description: "保持良好的社交关系，寻求社会支持",
            priority: .moderate,
            timeframe: .longTerm
        ))

        // 根据风险等级添加策略
        if riskLevel == .moderate || riskLevel == .high || riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .mental,
                title: "压力管理",
                description: "学习放松技巧，如冥想、深呼吸等",
                priority: .high,
                timeframe: .shortTerm
            ))
        }

        if riskLevel == .high || riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .mental,
                title: "心理咨询",
                description: "寻求专业心理健康服务",
                priority: .high,
                timeframe: .immediate
            ))

            strategies.append(PreventionStrategy(
                category: .lifestyle,
                title: "生活方式调整",
                description: "减少工作压力，改善工作生活平衡",
                priority: .moderate,
                timeframe: .mediumTerm
            ))
        }

        if riskLevel == .critical {
            strategies.append(PreventionStrategy(
                category: .medical,
                title: "医疗干预",
                description: "考虑药物治疗或住院治疗",
                priority: .high,
                timeframe: .immediate
            ))
        }

        return strategies
    }
}
