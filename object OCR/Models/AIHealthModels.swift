//
//  AIHealthModels.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation
import SwiftUI

// MARK: - AI健康分析结果
struct HealthAnalysisResult: Codable {
    let overallScore: Double
    let riskAssessment: RiskAssessment
    let lifestyleAnalysis: LifestyleAnalysis
    let mentalHealthScore: Double
    let nutritionAnalysis: NutritionAnalysis
    let fitnessAnalysis: FitnessAnalysis
    let recommendations: [HealthRecommendation]
    let analysisDate: Date
}

// MARK: - 风险评估
struct RiskAssessment: Codable {
    let overallRiskLevel: RiskLevel
    let specificRisks: [HealthRisk]
    let assessmentDate: Date
    let nextAssessmentDate: Date
}

struct HealthRisk: Codable {
    let condition: String
    let level: RiskLevel
    let probability: Double // 0-1
    let factors: [RiskFactor]
    let preventionStrategies: [String]
}

struct RiskFactor: Codable {
    let name: String
    let impact: RiskImpact
    let modifiable: Bool
    let description: String
}

enum RiskImpact: String, CaseIterable, Codable {
    case low = "低影响"
    case moderate = "中等影响"
    case high = "高影响"
    case critical = "严重影响"
}

// MARK: - 生活方式分析
struct LifestyleAnalysis: Codable {
    let score: Double
    let issues: [LifestyleIssue]
    let strengths: [LifestyleStrength]
    let recommendations: [String]
}

struct LifestyleIssue: Codable {
    let category: LifestyleCategory
    let severity: IssueSeverity
    let description: String
    let recommendation: String
}

struct LifestyleStrength: Codable {
    let category: LifestyleCategory
    let description: String
}

enum LifestyleCategory: String, CaseIterable, Codable {
    case sleep = "睡眠"
    case smoking = "吸烟"
    case alcohol = "饮酒"
    case diet = "饮食"
    case exercise = "运动"
    case stress = "压力管理"
    case social = "社交"
    case work = "工作"
}

enum IssueSeverity: String, CaseIterable, Codable {
    case low = "轻度"
    case moderate = "中度"
    case high = "重度"
    case critical = "严重"
}

// MARK: - 营养分析
struct NutritionAnalysis: Codable {
    let score: Double
    let deficiencies: [NutritionalDeficiency]
    let excesses: [NutritionalExcess]
    let recommendations: [NutritionRecommendation]
}

struct NutritionalDeficiency: Codable {
    let nutrient: String
    let severity: DeficiencySeverity
    let symptoms: [String]
    let foodSources: [String]
}

struct NutritionalExcess: Codable {
    let nutrient: String
    let level: ExcessLevel
    let healthRisks: [String]
    let reductionStrategies: [String]
}

enum DeficiencySeverity: String, CaseIterable, Codable {
    case mild = "轻度缺乏"
    case moderate = "中度缺乏"
    case severe = "严重缺乏"
}

enum ExcessLevel: String, CaseIterable, Codable {
    case mild = "轻度过量"
    case moderate = "中度过量"
    case severe = "严重过量"
}

// MARK: - 健身分析
struct FitnessAnalysis: Codable {
    let score: Double
    let strengths: [FitnessStrength]
    let weaknesses: [FitnessWeakness]
    let recommendations: [FitnessRecommendation]
}

struct FitnessStrength: Codable {
    let area: FitnessArea
    let description: String
    let level: FitnessLevel
}

struct FitnessWeakness: Codable {
    let area: FitnessArea
    let description: String
    let improvementPlan: String
}

enum FitnessArea: String, CaseIterable, Codable {
    case cardiovascular = "心血管健康"
    case strength = "力量"
    case flexibility = "柔韧性"
    case balance = "平衡性"
    case endurance = "耐力"
    case bodyComposition = "身体成分"
}

enum FitnessLevel: String, CaseIterable, Codable {
    case poor = "较差"
    case fair = "一般"
    case good = "良好"
    case excellent = "优秀"
}

// MARK: - 健康推荐
struct HealthRecommendation: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let category: HealthCategory
    let priority: RecommendationPriority
    let timeframe: Timeframe
    let actionSteps: [ActionStep]
    let expectedOutcome: String
    let trackingMetrics: [String]
}

struct ActionStep: Codable {
    let step: String
    let duration: String
    let frequency: String
    let difficulty: DifficultyLevel
}

enum RecommendationPriority: String, CaseIterable, Codable {
    case low = "低优先级"
    case medium = "中优先级"
    case high = "高优先级"
    case urgent = "紧急"
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .urgent: return .red
        }
    }
    
    var displayName: String {
        return self.rawValue
    }
}

enum Timeframe: String, CaseIterable, Codable {
    case immediate = "立即"
    case shortTerm = "短期(1-4周)"
    case mediumTerm = "中期(1-3个月)"
    case longTerm = "长期(3个月以上)"
}

enum DifficultyLevel: String, CaseIterable, Codable {
    case easy = "简单"
    case moderate = "中等"
    case hard = "困难"
    case veryHard = "很困难"
}

// MARK: - 营养推荐
struct NutritionRecommendation: Codable {
    let type: NutritionRecommendationType
    let description: String
    let specificFoods: [String]
    let portionSizes: [String]
    let timing: String
    let benefits: [String]
}

enum NutritionRecommendationType: String, CaseIterable, Codable {
    case increase = "增加摄入"
    case decrease = "减少摄入"
    case maintain = "保持现状"
    case avoid = "避免"
    case supplement = "补充"
}

// MARK: - 健身推荐
struct FitnessRecommendation: Codable {
    let exerciseType: ExerciseType
    let intensity: ExerciseIntensity
    let duration: String
    let frequency: String
    let progression: String
    let modifications: [String]
    let benefits: [String]
}

enum ExerciseIntensity: String, CaseIterable, Codable {
    case light = "轻度"
    case moderate = "中度"
    case vigorous = "高强度"
    case maximal = "最大强度"
}

// MARK: - 个性化健康计划
struct PersonalizedHealthPlan: Codable {
    let userId: UUID
    let dietPlan: DietPlan
    let exercisePlan: ExercisePlan
    let lifestylePlan: LifestylePlan
    let mentalHealthPlan: MentalHealthPlan
    let goals: [HealthGoal]
    let timeline: PlanTimeline
    let createdDate: Date
}

struct DietPlan: Codable {
    let dailyCalories: Int
    let macronutrients: Macronutrients
    let mealPlan: [MealPlan]
    let restrictions: [DietaryRestriction]
    let supplements: [SupplementRecommendation]
}

struct Macronutrients: Codable {
    let carbohydrates: Double // percentage
    let proteins: Double
    let fats: Double
}

struct MealPlan: Codable {
    let mealType: MealType
    let foods: [FoodItem]
    let totalCalories: Int
    let preparationTime: String
}

enum MealType: String, CaseIterable, Codable {
    case breakfast = "早餐"
    case lunch = "午餐"
    case dinner = "晚餐"
    case snack = "加餐"
}

struct FoodItem: Codable {
    let name: String
    let portion: String
    let calories: Int
    let nutrients: [String: Double]
}

enum DietaryRestriction: String, CaseIterable, Codable {
    case vegetarian = "素食"
    case vegan = "纯素食"
    case glutenFree = "无麸质"
    case dairyFree = "无乳制品"
    case lowSodium = "低钠"
    case lowSugar = "低糖"
    case halal = "清真"
    case kosher = "犹太洁食"
}

struct SupplementRecommendation: Codable {
    let name: String
    let dosage: String
    let timing: String
    let purpose: String
    let duration: String
}

// MARK: - 运动计划
struct ExercisePlan: Codable {
    let weeklySchedule: [WorkoutDay]
    let progressionPlan: ProgressionPlan
    let equipment: [Equipment]
    let safetyGuidelines: [String]
}

struct WorkoutDay: Codable {
    let day: String
    let exercises: [Exercise]
    let totalDuration: String
    let intensity: ExerciseIntensity
}

struct Exercise: Codable {
    let name: String
    let type: ExerciseType
    let sets: Int?
    let reps: String?
    let duration: String?
    let restPeriod: String?
    let instructions: [String]
}

struct ProgressionPlan: Codable {
    let week1to2: String
    let week3to4: String
    let week5to8: String
    let longTerm: String
}

enum Equipment: String, CaseIterable, Codable {
    case none = "无需器械"
    case dumbbells = "哑铃"
    case resistanceBands = "阻力带"
    case yogaMat = "瑜伽垫"
    case cardioMachine = "有氧器械"
    case gym = "健身房"
}

// MARK: - 生活方式计划
struct LifestylePlan: Codable {
    let sleepOptimization: SleepPlan
    let stressManagement: StressManagementPlan
    let habitChanges: [HabitChange]
    let environmentalModifications: [String]
}

struct SleepPlan: Codable {
    let targetSleepHours: Double
    let bedtime: String
    let wakeupTime: String
    let sleepHygieneTips: [String]
    let environmentOptimization: [String]
}

struct StressManagementPlan: Codable {
    let techniques: [StressManagementTechnique]
    let dailyPractices: [String]
    let emergencyStrategies: [String]
}

struct StressManagementTechnique: Codable {
    let name: String
    let description: String
    let duration: String
    let frequency: String
    let difficulty: DifficultyLevel
}

struct HabitChange: Codable {
    let currentHabit: String
    let targetHabit: String
    let strategy: String
    let timeline: String
    let milestones: [String]
}

// MARK: - 心理健康计划
struct MentalHealthPlan: Codable {
    let moodTracking: MoodTrackingPlan
    let mindfulnessPractices: [MindfulnessPractice]
    let socialActivities: [SocialActivity]
    let professionalSupport: ProfessionalSupportPlan?
}

struct MoodTrackingPlan: Codable {
    let frequency: String
    let metrics: [String]
    let triggers: [String]
    let copingStrategies: [String]
}

struct MindfulnessPractice: Codable {
    let name: String
    let description: String
    let duration: String
    let frequency: String
    let benefits: [String]
}

struct SocialActivity: Codable {
    let activity: String
    let frequency: String
    let benefits: [String]
    let suggestions: [String]
}

struct ProfessionalSupportPlan: Codable {
    let recommendationType: String
    let urgency: String
    let specializations: [String]
    let resources: [String]
}

// MARK: - 计划时间线
struct PlanTimeline: Codable {
    let phase1: TimelinePhase // 1-2 weeks
    let phase2: TimelinePhase // 3-4 weeks
    let phase3: TimelinePhase // 2-3 months
    let longTerm: TimelinePhase // 6+ months
}

struct TimelinePhase: Codable {
    let duration: String
    let goals: [String]
    let keyActivities: [String]
    let expectedOutcomes: [String]
    let assessmentPoints: [String]
}

// MARK: - 实时健康数据
struct RealTimeHealthData: Codable {
    let timestamp: Date
    let bloodPressure: BloodPressureReading?
    let heartRate: Int?
    let bloodSugar: Double?
    let oxygenSaturation: Double?
    let temperature: Double?
    let dailySteps: Int?
    let lastNightSleep: Double?
    let stressLevel: Int? // 1-10 scale
    let moodLevel: Int? // 1-10 scale
}

// MARK: - 即时建议
struct InstantAdvice: Identifiable, Codable {
    let id = UUID()
    let type: AdviceType
    let category: HealthCategory
    let title: String
    let message: String
    let action: String
    let timestamp: Date = Date()
}

enum AdviceType: String, CaseIterable, Codable {
    case suggestion = "建议"
    case warning = "警告"
    case urgent = "紧急"
    case information = "信息"
}
