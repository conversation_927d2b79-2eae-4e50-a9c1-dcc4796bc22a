//
//  HealthQuestion.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation

// 问题类型枚举
enum QuestionType: String, CaseIterable, Codable {
    case singleChoice = "single_choice"
    case multipleChoice = "multiple_choice"
    case scale = "scale"
    case yesNo = "yes_no"
    
    var displayName: String {
        switch self {
        case .singleChoice: return "单选题"
        case .multipleChoice: return "多选题"
        case .scale: return "量表题"
        case .yesNo: return "是非题"
        }
    }
}

// 健康分类
enum HealthCategory: String, CaseIterable, Codable {
    case physical = "physical"
    case mental = "mental"
    case lifestyle = "lifestyle"
    case nutrition = "nutrition"
    case sleep = "sleep"
    case exercise = "exercise"
    case work = "work"
    case social = "social"
    case environment = "environment"
    case preventive = "preventive"
    
    var displayName: String {
        switch self {
        case .physical: return "身体健康"
        case .mental: return "心理健康"
        case .lifestyle: return "生活习惯"
        case .nutrition: return "营养饮食"
        case .sleep: return "睡眠质量"
        case .exercise: return "运动锻炼"
        case .work: return "工作健康"
        case .social: return "社交健康"
        case .environment: return "环境健康"
        case .preventive: return "预防保健"
        }
    }

    var icon: String {
        switch self {
        case .physical:
            return "heart.fill"
        case .mental:
            return "brain.head.profile"
        case .lifestyle:
            return "house.fill"
        case .nutrition:
            return "leaf.fill"
        case .sleep:
            return "moon.fill"
        case .exercise:
            return "figure.run"
        case .work:
            return "briefcase.fill"
        case .social:
            return "person.2.fill"
        case .environment:
            return "globe.fill"
        case .preventive:
            return "shield.fill"
        }
    }
    
    var icon: String {
        switch self {
        case .physical: return "heart.fill"
        case .mental: return "brain.head.profile"
        case .lifestyle: return "house.fill"
        case .nutrition: return "leaf.fill"
        case .sleep: return "bed.double.fill"
        case .exercise: return "figure.run"
        case .work: return "briefcase.fill"
        case .social: return "person.2.fill"
        case .environment: return "tree.fill"
        case .preventive: return "shield.fill"
        }
    }
}

// 风险等级
enum RiskLevel: String, CaseIterable, Codable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .low: return "低风险"
        case .moderate: return "中等风险"
        case .high: return "高风险"
        case .critical: return "严重风险"
        }
    }
    
    var color: String {
        switch self {
        case .low: return "green"
        case .moderate: return "yellow"
        case .high: return "orange"
        case .critical: return "red"
        }
    }
}

// 性别枚举
enum Gender: String, CaseIterable, Codable {
    case male = "male"
    case female = "female"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .male: return "男"
        case .female: return "女"
        case .other: return "其他"
        }
    }
}

// 答案选项
struct AnswerOption: Identifiable, Codable {
    let id = UUID()
    let text: String
    let score: Int
    let description: String?
    
    init(text: String, score: Int, description: String? = nil) {
        self.text = text
        self.score = score
        self.description = description
    }
}

// 健康问题模型
struct HealthQuestion: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String?
    let type: QuestionType
    let category: HealthCategory
    let options: [AnswerOption]
    let minScore: Int?
    let maxScore: Int?
    let isRequired: Bool
    let order: Int
    
    init(title: String, description: String? = nil, type: QuestionType, category: HealthCategory, options: [AnswerOption], minScore: Int? = nil, maxScore: Int? = nil, isRequired: Bool = true, order: Int = 0) {
        self.title = title
        self.description = description
        self.type = type
        self.category = category
        self.options = options
        self.minScore = minScore
        self.maxScore = maxScore
        self.isRequired = isRequired
        self.order = order
    }
}

// 用户答案
struct UserAnswer: Identifiable, Codable {
    let id = UUID()
    let questionId: UUID
    let selectedOptionIds: [UUID]
    let scaleValue: Double?
    let textAnswer: String?
    let answeredAt: Date
    
    init(questionId: UUID, selectedOptionIds: [UUID] = [], scaleValue: Double? = nil, textAnswer: String? = nil) {
        self.questionId = questionId
        self.selectedOptionIds = selectedOptionIds
        self.scaleValue = scaleValue
        self.textAnswer = textAnswer
        self.answeredAt = Date()
    }
}

// 健康评估结果
struct HealthAssessment: Identifiable, Codable {
    let id = UUID()
    let userId: String?
    let answers: [UserAnswer]
    let totalScore: Int
    let categoryScores: [HealthCategory: Int]
    let recommendations: [String]
    let riskLevel: RiskLevel
    let overallRiskLevel: RiskLevel
    let createdAt: Date
    
    init(userId: String? = nil, answers: [UserAnswer], totalScore: Int, categoryScores: [HealthCategory: Int], recommendations: [String], riskLevel: RiskLevel, overallRiskLevel: RiskLevel) {
        self.userId = userId
        self.answers = answers
        self.totalScore = totalScore
        self.categoryScores = categoryScores
        self.recommendations = recommendations
        self.riskLevel = riskLevel
        self.overallRiskLevel = overallRiskLevel
        self.createdAt = Date()
    }
}

// 用户档案
struct UserProfile: Identifiable, Codable {
    let id = UUID()
    var name: String?
    var age: Int?
    var gender: Gender?
    var height: Double? // cm
    var weight: Double? // kg
    var medicalHistory: [String]
    var allergies: [String]
    var medications: [String]
    var emergencyContact: String?
    var createdAt: Date
    var updatedAt: Date
    
    init(name: String? = nil, age: Int? = nil, gender: Gender? = nil, height: Double? = nil, weight: Double? = nil, medicalHistory: [String] = [], allergies: [String] = [], medications: [String] = [], emergencyContact: String? = nil) {
        self.name = name
        self.age = age
        self.gender = gender
        self.height = height
        self.weight = weight
        self.medicalHistory = medicalHistory
        self.allergies = allergies
        self.medications = medications
        self.emergencyContact = emergencyContact
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    var bmi: Double? {
        guard let height = height, let weight = weight, height > 0 else { return nil }
        let heightInMeters = height / 100
        return weight / (heightInMeters * heightInMeters)
    }
    
    var bmiCategory: String {
        guard let bmi = bmi else { return "未知" }

        switch bmi {
        case ..<18.5: return "偏瘦"
        case 18.5..<24: return "正常"
        case 24..<28: return "超重"
        default: return "肥胖"
        }
    }
}

// 健康数据条目
struct HealthDataEntry: Identifiable, Codable {
    let id = UUID()
    let category: HealthCategory
    let value: Double
    let unit: String
    let notes: String?
    let recordedAt: Date

    init(category: HealthCategory, value: Double, unit: String, notes: String? = nil) {
        self.category = category
        self.value = value
        self.unit = unit
        self.notes = notes
        self.recordedAt = Date()
    }
}

// 健康目标
struct HealthGoal: Identifiable, Codable {
    let id = UUID()
    let category: HealthCategory
    let title: String
    let description: String
    let targetValue: Double
    let currentValue: Double
    let unit: String
    let deadline: Date
    let isCompleted: Bool
    let createdAt: Date

    init(category: HealthCategory, title: String, description: String, targetValue: Double, currentValue: Double = 0, unit: String, deadline: Date) {
        self.category = category
        self.title = title
        self.description = description
        self.targetValue = targetValue
        self.currentValue = currentValue
        self.unit = unit
        self.deadline = deadline
        self.isCompleted = currentValue >= targetValue
        self.createdAt = Date()
    }

    var progress: Double {
        guard targetValue > 0 else { return 0 }
        return min(currentValue / targetValue, 1.0)
    }
}

// 健康提醒
struct HealthReminder: Identifiable, Codable {
    let id = UUID()
    let title: String
    let message: String
    let category: HealthCategory
    let scheduledTime: Date
    let isRepeating: Bool
    let isCompleted: Bool
    let createdAt: Date

    init(title: String, message: String, category: HealthCategory, scheduledTime: Date, isRepeating: Bool = false) {
        self.title = title
        self.message = message
        self.category = category
        self.scheduledTime = scheduledTime
        self.isRepeating = isRepeating
        self.isCompleted = false
        self.createdAt = Date()
    }
}

// 健康报告
struct HealthReport: Identifiable, Codable {
    let id = UUID()
    let title: String
    let generatedDate: Date
    let reportPeriod: String
    let summary: String
    let assessmentAnalysis: String
    let dataAnalysis: String
    let goalAnalysis: String
    let recommendations: [String]
}
