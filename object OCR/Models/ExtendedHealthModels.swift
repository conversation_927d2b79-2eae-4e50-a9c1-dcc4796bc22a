//
//  ExtendedHealthModels.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import Foundation

// MARK: - 扩展的用户健康档案
struct ExtendedUserProfile: Codable {
    var id: UUID = UUID()
    var basicInfo: DetailedBasicInfo
    var medicalHistory: DetailedMedicalHistory
    var lifestyle: DetailedLifestyle
    var mentalHealth: MentalHealthProfile
    var physicalActivity: PhysicalActivityProfile
    var nutritionProfile: NutritionProfile
    var aiRecommendations: AIRecommendations
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
}

// MARK: - 详细基本信息
struct DetailedBasicInfo: Codable {
    var name: String = ""
    var age: Int = 0
    var gender: Gender = .other
    var height: Double = 0.0 // cm
    var weight: Double = 0.0 // kg
    var bloodType: BloodType = .unknown
    var emergencyContact: String = ""
    var occupation: String = ""
    var maritalStatus: MaritalStatus = .single
    var education: EducationLevel = .unknown
    var income: IncomeLevel = .notSpecified
    var location: String = ""
    var preferredLanguage: Language = .chinese
}

// MARK: - 详细病史
struct DetailedMedicalHistory: Codable {
    var bloodPressure: BloodPressureReading = BloodPressureReading()
    var bloodSugar: BloodSugarReading = BloodSugarReading()
    var cholesterol: CholesterolReading = CholesterolReading()
    var heartRate: Int = 70
    var chronicDiseases: [ChronicDisease] = []
    var allergies: [AllergyInfo] = []
    var medications: [MedicationInfo] = []
    var familyHistory: [FamilyMedicalHistory] = []
    var surgicalHistory: [SurgicalHistory] = []
    var vaccinationHistory: [VaccinationRecord] = []
    var lastCheckup: Date?
    var doctorNotes: String = ""
}

// MARK: - 详细生活方式
struct DetailedLifestyle: Codable {
    var sleepPattern: SleepPattern
    var smokingHistory: SmokingHistory
    var alcoholConsumption: AlcoholConsumption
    var dietaryHabits: DietaryHabits
    var stressFactors: [StressFactor] = []
    var workEnvironment: WorkEnvironment
    var livingEnvironment: LivingEnvironment
    var socialSupport: SocialSupport
}

// MARK: - 心理健康档案
struct MentalHealthProfile: Codable {
    var currentMood: MoodLevel = .neutral
    var anxietyLevel: AnxietyLevel = .low
    var depressionScreening: DepressionScreening
    var stressLevel: StressLevel = .moderate
    var copingMechanisms: [CopingMechanism] = []
    var socialConnections: SocialConnections
    var workLifeBalance: WorkLifeBalance = .balanced
    var mentalHealthHistory: [MentalHealthEvent] = []
    var therapyHistory: [TherapyRecord] = []
}

// MARK: - 运动健康档案
struct PhysicalActivityProfile: Codable {
    var currentActivityLevel: ActivityLevel = .moderate
    var exerciseRoutine: ExerciseRoutine
    var sportsParticipation: [SportActivity] = []
    var fitnessGoals: [FitnessGoal] = []
    var physicalLimitations: [PhysicalLimitation] = []
    var injuryHistory: [InjuryRecord] = []
    var dailyMovement: DailyMovementPattern
}

// MARK: - 营养档案
struct NutritionProfile: Codable {
    var dietaryPreferences: DietaryPreferences
    var nutritionalGoals: [NutritionalGoal] = []
    var foodAllergies: [FoodAllergy] = []
    var supplementation: [SupplementInfo] = []
    var eatingPatterns: EatingPatterns
    var hydrationHabits: HydrationHabits
    var culturalDietaryFactors: [String] = []
}

// MARK: - AI推荐系统
struct AIRecommendations: Codable {
    var personalizedDietPlan: DietPlan?
    var exerciseRecommendations: [ExerciseRecommendation] = []
    var lifestyleModifications: [LifestyleRecommendation] = []
    var healthRiskAssessment: HealthRiskAssessment?
    var preventiveCareReminders: [PreventiveCareReminder] = []
    var mentalHealthSupport: [MentalHealthRecommendation] = []
    var lastUpdated: Date = Date()
}

// MARK: - 支持结构体定义

enum BloodType: String, CaseIterable, Codable {
    case aPositive = "A+"
    case aNegative = "A-"
    case bPositive = "B+"
    case bNegative = "B-"
    case abPositive = "AB+"
    case abNegative = "AB-"
    case oPositive = "O+"
    case oNegative = "O-"
    case unknown = "未知"
}

enum MaritalStatus: String, CaseIterable, Codable {
    case single = "单身"
    case married = "已婚"
    case divorced = "离异"
    case widowed = "丧偶"
    case partnered = "同居"
}

enum EducationLevel: String, CaseIterable, Codable {
    case elementary = "小学"
    case middleSchool = "初中"
    case highSchool = "高中"
    case college = "大专"
    case bachelor = "本科"
    case master = "硕士"
    case doctorate = "博士"
    case unknown = "未知"
}

enum IncomeLevel: String, CaseIterable, Codable {
    case low = "低收入"
    case middleLow = "中低收入"
    case middle = "中等收入"
    case middleHigh = "中高收入"
    case high = "高收入"
    case notSpecified = "不愿透露"
}

enum Language: String, CaseIterable, Codable {
    case chinese = "中文"
    case uyghur = "维吾尔语"
    case english = "英语"
    case kazakh = "哈萨克语"
    case kyrgyz = "柯尔克孜语"
}

struct BloodPressureReading: Codable {
    var systolic: Int = 120
    var diastolic: Int = 80
    var recordedAt: Date = Date()
    
    var level: BloodPressureLevel {
        switch (systolic, diastolic) {
        case (..<90, ..<60): return .low
        case (..<120, ..<80): return .normal
        case (120..<130, ..<80): return .elevated
        case (130..<140, 80..<90): return .stage1Hypertension
        case (140..., 90...): return .stage2Hypertension
        default: return .normal
        }
    }
}

enum BloodPressureLevel: String, CaseIterable, Codable {
    case low = "低血压"
    case normal = "正常"
    case elevated = "血压偏高"
    case stage1Hypertension = "高血压1期"
    case stage2Hypertension = "高血压2期"
}

struct BloodSugarReading: Codable {
    var fastingGlucose: Double = 0.0 // mg/dL
    var postMealGlucose: Double = 0.0
    var hba1c: Double = 0.0 // %
    var recordedAt: Date = Date()
    
    var level: BloodSugarLevel {
        if fastingGlucose < 70 { return .low }
        if fastingGlucose < 100 { return .normal }
        if fastingGlucose < 126 { return .prediabetes }
        return .diabetes
    }
}

enum BloodSugarLevel: String, CaseIterable, Codable {
    case low = "低血糖"
    case normal = "正常"
    case prediabetes = "糖尿病前期"
    case diabetes = "糖尿病"
}

struct CholesterolReading: Codable {
    var totalCholesterol: Double = 0.0 // mg/dL
    var ldl: Double = 0.0
    var hdl: Double = 0.0
    var triglycerides: Double = 0.0
    var recordedAt: Date = Date()
}

enum ChronicDisease: String, CaseIterable, Codable {
    case diabetes = "糖尿病"
    case hypertension = "高血压"
    case heartDisease = "心脏病"
    case asthma = "哮喘"
    case arthritis = "关节炎"
    case depression = "抑郁症"
    case anxiety = "焦虑症"
    case obesity = "肥胖症"
    case osteoporosis = "骨质疏松"
    case thyroidDisorder = "甲状腺疾病"
    case kidneyDisease = "肾脏疾病"
    case liverDisease = "肝脏疾病"
    case cancer = "癌症"
    case stroke = "中风"
    case copd = "慢性阻塞性肺病"
}

struct AllergyInfo: Codable {
    var allergen: String
    var severity: AllergySeverity
    var symptoms: [String]
    var treatment: String
    var diagnosedDate: Date?
}

enum AllergySeverity: String, CaseIterable, Codable {
    case mild = "轻度"
    case moderate = "中度"
    case severe = "重度"
    case lifeThreatening = "危及生命"
}

struct MedicationInfo: Codable {
    var name: String
    var dosage: String
    var frequency: String
    var purpose: String
    var startDate: Date
    var endDate: Date?
    var sideEffects: [String]
    var prescribedBy: String
}

struct FamilyMedicalHistory: Codable {
    var relationship: FamilyRelationship
    var condition: String
    var ageOfOnset: Int?
    var notes: String
}

enum FamilyRelationship: String, CaseIterable, Codable {
    case parent = "父母"
    case sibling = "兄弟姐妹"
    case grandparent = "祖父母/外祖父母"
    case child = "子女"
    case other = "其他亲属"
}

struct SurgicalHistory: Codable {
    var procedure: String
    var date: Date
    var hospital: String
    var surgeon: String
    var complications: String?
    var outcome: String
}

struct VaccinationRecord: Codable {
    var vaccine: String
    var date: Date
    var location: String
    var batchNumber: String?
    var nextDueDate: Date?
}

// MARK: - 睡眠模式
struct SleepPattern: Codable {
    var averageSleepHours: Double = 8.0
    var bedtime: String = "22:00"
    var wakeupTime: String = "06:00"
    var sleepQuality: SleepQuality = .good
    var sleepDisorders: [SleepDisorder] = []
    var napsFrequency: NapFrequency = .never
    var sleepEnvironment: SleepEnvironment = SleepEnvironment()
}

enum SleepQuality: String, CaseIterable, Codable {
    case excellent = "优秀"
    case good = "良好"
    case fair = "一般"
    case poor = "较差"
    case veryPoor = "很差"
}

enum SleepDisorder: String, CaseIterable, Codable {
    case insomnia = "失眠"
    case sleepApnea = "睡眠呼吸暂停"
    case restlessLeg = "不宁腿综合征"
    case narcolepsy = "嗜睡症"
    case nightTerrors = "夜惊"
    case sleepwalking = "梦游"
}

enum NapFrequency: String, CaseIterable, Codable {
    case never = "从不"
    case rarely = "很少"
    case sometimes = "偶尔"
    case regularly = "经常"
    case daily = "每天"
}

struct SleepEnvironment: Codable {
    var roomTemperature: Double = 20.0 // 摄氏度
    var noiseLevel: NoiseLevel = .quiet
    var lightLevel: LightLevel = .dark
    var mattressComfort: ComfortLevel = .comfortable
}

enum NoiseLevel: String, CaseIterable, Codable {
    case silent = "安静"
    case quiet = "较安静"
    case moderate = "中等"
    case noisy = "嘈杂"
    case veryNoisy = "很嘈杂"
}

enum LightLevel: String, CaseIterable, Codable {
    case dark = "黑暗"
    case dim = "昏暗"
    case moderate = "中等"
    case bright = "明亮"
    case veryBright = "很亮"
}

enum ComfortLevel: String, CaseIterable, Codable {
    case veryComfortable = "非常舒适"
    case comfortable = "舒适"
    case neutral = "一般"
    case uncomfortable = "不舒适"
    case veryUncomfortable = "很不舒适"
}

// MARK: - 吸烟史
struct SmokingHistory: Codable {
    var status: SmokingStatus = .never
    var startAge: Int?
    var quitAge: Int?
    var cigarettesPerDay: Int = 0
    var yearsSmoked: Int = 0
    var quitAttempts: Int = 0
    var smokingTriggers: [SmokingTrigger] = []
}

enum SmokingStatus: String, CaseIterable, Codable {
    case never = "从不吸烟"
    case former = "已戒烟"
    case occasional = "偶尔吸烟"
    case regular = "经常吸烟"
    case heavy = "重度吸烟"
}

enum SmokingTrigger: String, CaseIterable, Codable {
    case stress = "压力"
    case social = "社交"
    case alcohol = "饮酒时"
    case work = "工作时"
    case boredom = "无聊时"
    case habit = "习惯性"
}

// MARK: - 饮酒消费
struct AlcoholConsumption: Codable {
    var frequency: AlcoholFrequency = .never
    var drinksPerWeek: Int = 0
    var preferredType: [AlcoholType] = []
    var bingeDrinking: Bool = false
    var drinkingTriggers: [DrinkingTrigger] = []
    var alcoholProblems: [AlcoholProblem] = []
}

enum AlcoholFrequency: String, CaseIterable, Codable {
    case never = "从不饮酒"
    case rarely = "很少饮酒"
    case occasionally = "偶尔饮酒"
    case moderately = "适量饮酒"
    case frequently = "经常饮酒"
    case daily = "每天饮酒"
}

enum AlcoholType: String, CaseIterable, Codable {
    case beer = "啤酒"
    case wine = "葡萄酒"
    case spirits = "烈酒"
    case cocktails = "鸡尾酒"
    case traditional = "传统酒类"
}

enum DrinkingTrigger: String, CaseIterable, Codable {
    case stress = "压力"
    case social = "社交"
    case celebration = "庆祝"
    case relaxation = "放松"
    case habit = "习惯"
    case depression = "抑郁"
}

enum AlcoholProblem: String, CaseIterable, Codable {
    case blackouts = "断片"
    case dependence = "依赖"
    case withdrawal = "戒断症状"
    case socialProblems = "社交问题"
    case workProblems = "工作问题"
    case healthProblems = "健康问题"
}

// MARK: - 饮食习惯
struct DietaryHabits: Codable {
    var dietType: DietType = .balanced
    var mealsPerDay: Int = 3
    var snackingFrequency: SnackingFrequency = .sometimes
    var cookingFrequency: CookingFrequency = .sometimes
    var fastFoodFrequency: FastFoodFrequency = .rarely
    var vegetableServings: Int = 3
    var fruitServings: Int = 2
    var waterIntake: Double = 2.0 // liters
    var culturalDietaryRestrictions: [String] = []
}

enum DietType: String, CaseIterable, Codable {
    case balanced = "均衡饮食"
    case vegetarian = "素食"
    case vegan = "纯素食"
    case keto = "生酮饮食"
    case mediterranean = "地中海饮食"
    case lowCarb = "低碳水化合物"
    case highProtein = "高蛋白"
    case traditional = "传统饮食"
    case halal = "清真饮食"
    case kosher = "犹太饮食"
}

enum SnackingFrequency: String, CaseIterable, Codable {
    case never = "从不"
    case rarely = "很少"
    case sometimes = "偶尔"
    case often = "经常"
    case constantly = "总是"
}

enum CookingFrequency: String, CaseIterable, Codable {
    case never = "从不做饭"
    case rarely = "很少做饭"
    case sometimes = "偶尔做饭"
    case often = "经常做饭"
    case daily = "每天做饭"
}

enum FastFoodFrequency: String, CaseIterable, Codable {
    case never = "从不"
    case rarely = "很少"
    case sometimes = "偶尔"
    case often = "经常"
    case daily = "每天"
}

// MARK: - 压力因素
struct StressFactor: Codable {
    var source: StressSource
    var intensity: StressIntensity
    var frequency: StressFrequency
    var copingStrategy: String
}

enum StressSource: String, CaseIterable, Codable {
    case work = "工作压力"
    case family = "家庭压力"
    case financial = "经济压力"
    case health = "健康担忧"
    case relationships = "人际关系"
    case academic = "学业压力"
    case social = "社会压力"
}

enum StressIntensity: String, CaseIterable, Codable {
    case low = "轻度"
    case moderate = "中度"
    case high = "重度"
    case severe = "严重"
}

enum StressFrequency: String, CaseIterable, Codable {
    case rarely = "很少"
    case sometimes = "偶尔"
    case often = "经常"
    case daily = "每天"
    case constantly = "持续"
}

// MARK: - 工作环境
struct WorkEnvironment: Codable {
    var workType: WorkType = .office
    var workHours: Double = 8.0
    var physicalDemands: PhysicalDemands = .light
    var workStressLevel: StressLevel = .moderate
    var workSatisfaction: SatisfactionLevel = .satisfied
    var commuteDuration: Double = 30.0 // minutes
    var workplaceHazards: [WorkplaceHazard] = []
}

enum WorkType: String, CaseIterable, Codable {
    case office = "办公室工作"
    case manual = "体力劳动"
    case healthcare = "医疗工作"
    case education = "教育工作"
    case retail = "零售服务"
    case technology = "技术工作"
    case agriculture = "农业工作"
    case transportation = "交通运输"
    case unemployed = "无业"
    case retired = "退休"
    case student = "学生"
}

enum PhysicalDemands: String, CaseIterable, Codable {
    case sedentary = "久坐"
    case light = "轻度体力"
    case moderate = "中度体力"
    case heavy = "重体力"
    case veryHeavy = "极重体力"
}

enum SatisfactionLevel: String, CaseIterable, Codable {
    case veryDissatisfied = "非常不满意"
    case dissatisfied = "不满意"
    case neutral = "一般"
    case satisfied = "满意"
    case verySatisfied = "非常满意"
}

enum WorkplaceHazard: String, CaseIterable, Codable {
    case chemicals = "化学物质"
    case noise = "噪音"
    case radiation = "辐射"
    case dust = "粉尘"
    case ergonomicRisks = "人体工程学风险"
    case biologicalHazards = "生物危害"
    case psychologicalStress = "心理压力"
}

// MARK: - 居住环境
struct LivingEnvironment: Codable {
    var housingType: HousingType = .apartment
    var airQuality: AirQuality = .good
    var noiseLevel: NoiseLevel = .quiet
    var safetyLevel: SafetyLevel = .safe
    var accessToGreenSpace: Bool = true
    var accessToHealthcare: AccessLevel = .good
    var accessToExerciseFacilities: AccessLevel = .good
    var communitySupport: CommunitySupport = .moderate
}

enum HousingType: String, CaseIterable, Codable {
    case apartment = "公寓"
    case house = "独立房屋"
    case townhouse = "联排别墅"
    case dormitory = "宿舍"
    case sharedHousing = "合租房"
    case other = "其他"
}

enum AirQuality: String, CaseIterable, Codable {
    case excellent = "优秀"
    case good = "良好"
    case moderate = "中等"
    case poor = "较差"
    case veryPoor = "很差"
}

enum SafetyLevel: String, CaseIterable, Codable {
    case verySafe = "非常安全"
    case safe = "安全"
    case moderate = "一般"
    case unsafe = "不安全"
    case veryUnsafe = "很不安全"
}

enum AccessLevel: String, CaseIterable, Codable {
    case excellent = "优秀"
    case good = "良好"
    case moderate = "中等"
    case poor = "较差"
    case veryPoor = "很差"
}

enum CommunitySupport: String, CaseIterable, Codable {
    case strong = "强"
    case moderate = "中等"
    case weak = "弱"
    case none = "无"
}

// MARK: - 社会支持
struct SocialSupport: Codable {
    var familySupport: SupportLevel = .good
    var friendSupport: SupportLevel = .good
    var communityInvolvement: InvolvementLevel = .moderate
    var socialNetworkSize: SocialNetworkSize = .moderate
    var loneliness: LonelinessLevel = .low
    var culturalConnections: Bool = true
}

enum SupportLevel: String, CaseIterable, Codable {
    case excellent = "优秀"
    case good = "良好"
    case moderate = "中等"
    case poor = "较差"
    case veryPoor = "很差"
}

enum InvolvementLevel: String, CaseIterable, Codable {
    case veryActive = "非常活跃"
    case active = "活跃"
    case moderate = "中等"
    case limited = "有限"
    case none = "无"
}

enum SocialNetworkSize: String, CaseIterable, Codable {
    case large = "大"
    case moderate = "中等"
    case small = "小"
    case verySmall = "很小"
}

enum LonelinessLevel: String, CaseIterable, Codable {
    case none = "无"
    case low = "轻度"
    case moderate = "中度"
    case high = "重度"
    case severe = "严重"
}
