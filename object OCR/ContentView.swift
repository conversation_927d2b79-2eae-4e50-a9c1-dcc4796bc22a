//
//  ContentView.swift
//  object OCR
//
//  Created by apple on 2025/7/29.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var questionService = HealthQuestionService()
    @StateObject private var assessmentService = HealthAssessmentService()
    @StateObject private var userProfileService = UserProfileService()
    @StateObject private var healthDataService = HealthDataService()
    @StateObject private var reportService = HealthReportService()
    
    @State private var selectedTab = 0
    @State private var showingQuestionnaire = false
    @State private var showingProfile = false
    @State private var showingDataEntry = false
    @State private var showingReport = false
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 主页
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("主页")
                }
                .tag(0)
            
            // 健康评估
            AssessmentView()
                .tabItem {
                    Image(systemName: "heart.text.square.fill")
                    Text("健康评估")
                }
                .tag(1)
            
            // 数据追踪
            TrackingView()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("数据追踪")
                }
                .tag(2)
            
            // 健康报告
            ReportView()
                .tabItem {
                    Image(systemName: "doc.text.fill")
                    Text("健康报告")
                }
                .tag(3)
            
            // 个人中心
            ProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("个人中心")
                }
                .tag(4)
        }
        .environmentObject(questionService)
        .environmentObject(assessmentService)
        .environmentObject(userProfileService)
        .environmentObject(healthDataService)
        .environmentObject(reportService)
    }
}

// 主页视图
struct HomeView: View {
    @EnvironmentObject var assessmentService: HealthAssessmentService
    @EnvironmentObject var userProfileService: UserProfileService
    @EnvironmentObject var healthDataService: HealthDataService
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 欢迎信息
                    WelcomeCard()
                    
                    // 健康概览
                    HealthOverviewCard()
                    
                    // 快速操作
                    QuickActionsCard()
                    
                    // 最近活动
                    RecentActivityCard()
                }
                .padding()
            }
            .navigationTitle("健康管理")
            .background(Color(.systemGroupedBackground))
        }
    }
}

// 欢迎卡片
struct WelcomeCard: View {
    @EnvironmentObject var userProfileService: UserProfileService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(greeting)
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text(welcomeMessage)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "sun.max.fill")
                    .font(.title)
                    .foregroundColor(.orange)
            }
            
            // 个人信息完整度
            if let profile = userProfileService.userProfile {
                let completeness = userProfileService.getProfileCompleteness()
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("个人信息完整度")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text("\(Int(completeness * 100))%")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    
                    ProgressView(value: completeness)
                        .progressViewStyle(LinearProgressViewStyle(tint: completeness > 0.7 ? .green : .orange))
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private var greeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "早上好"
        case 12..<17: return "下午好"
        case 17..<22: return "晚上好"
        default: return "夜深了"
        }
    }
    
    private var welcomeMessage: String {
        if let name = userProfileService.userProfile?.name {
            return "\(name)，关注健康，享受生活"
        } else {
            return "欢迎使用健康管理系统"
        }
    }
}

// 健康概览卡片
struct HealthOverviewCard: View {
    @EnvironmentObject var assessmentService: HealthAssessmentService
    @EnvironmentObject var healthDataService: HealthDataService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("健康概览")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let latestAssessment = assessmentService.latestAssessment {
                VStack(spacing: 12) {
                    // 总体健康状况
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("健康评分")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text("\(latestAssessment.totalScore)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(riskLevelColor(latestAssessment.overallRiskLevel))
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("风险等级")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text(latestAssessment.overallRiskLevel.displayName)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(riskLevelColor(latestAssessment.overallRiskLevel))
                        }
                    }
                    
                    // 评估日期
                    HStack {
                        Text("最近评估：\(formatDate(latestAssessment.createdAt))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                    }
                }
            } else {
                VStack(spacing: 8) {
                    Image(systemName: "heart.circle")
                        .font(.largeTitle)
                        .foregroundColor(.gray)
                    
                    Text("暂无健康评估数据")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("点击开始您的第一次健康评估")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical)
            }
            
            // 数据统计
            HStack(spacing: 20) {
                StatisticItem(
                    title: "数据记录",
                    value: "\(healthDataService.healthData.count)",
                    icon: "chart.bar.fill",
                    color: .blue
                )
                
                StatisticItem(
                    title: "健康目标",
                    value: "\(healthDataService.healthGoals.count)",
                    icon: "target",
                    color: .green
                )
                
                StatisticItem(
                    title: "完成率",
                    value: "\(Int(healthDataService.getGoalCompletionRate() * 100))%",
                    icon: "checkmark.circle.fill",
                    color: .orange
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func riskLevelColor(_ riskLevel: RiskLevel) -> Color {
        switch riskLevel {
        case .low: return .green
        case .moderate: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// 统计项目
struct StatisticItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// 快速操作卡片
struct QuickActionsCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("快速操作")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                QuickActionButton(
                    title: "健康评估",
                    subtitle: "开始问卷调查",
                    icon: "heart.text.square",
                    color: .red,
                    destination: AnyView(HealthQuestionnaireView())
                )
                
                QuickActionButton(
                    title: "记录数据",
                    subtitle: "添加健康数据",
                    icon: "plus.circle",
                    color: .blue,
                    destination: AnyView(AddHealthDataView())
                )
                
                QuickActionButton(
                    title: "查看报告",
                    subtitle: "生成健康报告",
                    icon: "doc.text",
                    color: .green,
                    destination: AnyView(ReportView())
                )
                
                QuickActionButton(
                    title: "个人档案",
                    subtitle: "管理个人信息",
                    icon: "person.circle",
                    color: .purple,
                    destination: AnyView(UserProfileView())
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// 快速操作按钮
struct QuickActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let destination: AnyView
    
    var body: some View {
        NavigationLink(destination: destination) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.secondarySystemBackground))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 最近活动卡片
struct RecentActivityCard: View {
    @EnvironmentObject var healthDataService: HealthDataService
    @EnvironmentObject var assessmentService: HealthAssessmentService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("最近活动")
                .font(.headline)
                .fontWeight(.semibold)
            
            let recentData = healthDataService.getRecentHealthData(days: 7)
            let recentAssessments = Array(assessmentService.assessmentHistory.prefix(3))
            
            if recentData.isEmpty && recentAssessments.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "clock")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text("暂无最近活动")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical)
            } else {
                VStack(spacing: 12) {
                    // 最近的健康数据
                    ForEach(recentData.prefix(3)) { entry in
                        ActivityRow(
                            title: entry.category.displayName,
                            subtitle: "\(entry.value, specifier: "%.1f") \(entry.unit)",
                            time: formatTime(entry.recordedAt),
                            icon: entry.category.icon,
                            color: .blue
                        )
                    }
                    
                    // 最近的评估
                    ForEach(recentAssessments) { assessment in
                        ActivityRow(
                            title: "健康评估",
                            subtitle: "总分 \(assessment.totalScore) 分",
                            time: formatTime(assessment.createdAt),
                            icon: "heart.text.square",
                            color: .red
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// 活动行
struct ActivityRow: View {
    let title: String
    let subtitle: String
    let time: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(time)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    ContentView()
}
